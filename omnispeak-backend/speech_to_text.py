"""
Speech-to-Text module for live audio transcription
"""
import asyncio
import logging
from typing import AsyncGenerator, Optional, Callable, Generator
from google.cloud import speech
import wave
import io
from dataclasses import dataclass
import time
import config

logger = logging.getLogger(__name__)

@dataclass
class TranscriptionResult:
    text: str
    is_final: bool
    confidence: float
    timestamp: float
    language: str

class SpeechToText:
    """
    Real-time speech-to-text transcription using Google Cloud Speech-to-Text
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        self.client = speech.SpeechClient()
        self.is_streaming = False
        self.audio_buffer = bytearray()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription"""
        if self.is_streaming:
            return
            
        self.is_streaming = True
        logger.info("Starting streaming speech-to-text transcription")
        
        # Configure streaming recognition
        config_obj = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=self.sample_rate,
            language_code=self.source_language if self.source_language != "auto" else "en-US",
            enable_automatic_punctuation=True,
            enable_word_time_offsets=False,
            model="latest_long",
            use_enhanced=True,
        )
        
        streaming_config = speech.StreamingRecognitionConfig(
            config=config_obj,
            interim_results=True,
            single_utterance=False,
        )
        
        try:
            # The audio generator needs to be a synchronous generator for the client library
            # We'll use a queue to pass data from the async `add_audio_chunk` to the sync generator
            audio_queue = asyncio.Queue()
            self._audio_queue = audio_queue

            # Start a separate task to consume the responses
            response_processor = asyncio.create_task(self._process_responses(
                self.client.streaming_recognize(streaming_config, self._audio_generator())
            ))

            # Wait for the processing to finish
            await response_processor

        except Exception as e:
            logger.error(f"Error in streaming transcription: {e}")
        finally:
            self.is_streaming = False
            
    def _audio_generator(self) -> Generator[bytes, None, None]:
        """Synchronous generator to yield audio chunks from the queue."""
        while self.is_streaming or not self._audio_queue.empty():
            try:
                # Use a small timeout to allow the loop to check `is_streaming`
                chunk = self._audio_queue.get_nowait()
                yield chunk
            except asyncio.QueueEmpty:
                if not self.is_streaming:
                  break
                # Wait briefly if the queue is empty
                time.sleep(0.01)

    async def _process_responses(self, responses) -> None:
        """Process streaming recognition responses"""
        try:
           for response in responses:
            if not response.results:
                continue

            print(f"Received response: {response}")

            result = response.results[0]
            if not result.alternatives:
                continue
                
            alternative = result.alternatives[0]
            
            transcription_result = TranscriptionResult(
                text=alternative.transcript,
                is_final=result.is_final,
                confidence=alternative.confidence if hasattr(alternative, 'confidence') else 0.0,
                timestamp=time.time(),
                language=self.source_language
            )
            
            if self.transcription_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.transcription_callback, transcription_result
                )
                self.transcription_callback(transcription_result)

        except Exception as e:
            logger.error(f"Error processing streaming responses: {e}")

        finally:
           # Stop the main streaming loop when the response iterator finishes
           await self.stop_streaming_transcription()

    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the transcription buffer"""
        if self.is_streaming:
            await self._audio_queue.put(audio_data)
            
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        self.is_streaming = False
        self.audio_buffer.clear()
        logger.info("Stopped streaming speech-to-text transcription")

class FallbackSpeechToText:
    """
    Fallback STT implementation using Gemini for when Google Cloud STT is not available
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        self.is_streaming = False
        self.audio_buffer = bytearray()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.accumulated_audio = bytearray()
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription using Gemini"""
        if self.is_streaming:
            return
            
        self.is_streaming = True
        logger.info("Starting fallback streaming speech-to-text using Gemini")
        
        # Start processing task
        asyncio.create_task(self._process_audio_chunks())
        
    async def _process_audio_chunks(self):
        """Process audio chunks periodically for transcription"""
        from google import genai
        from google.genai import types
        
        client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        
        while self.is_streaming:
            await asyncio.sleep(2.0)  # Process every 2 seconds
            
            if len(self.accumulated_audio) < self.sample_rate * 1:  # At least 1 second of audio
                continue
                
            try:
                # Convert audio to WAV format
                audio_data = bytes(self.accumulated_audio)
                wav_buffer = io.BytesIO()
                
                with wave.open(wav_buffer, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data)
                
                wav_data = wav_buffer.getvalue()
                
                # Use Gemini for transcription
                response = client.models.generate_content(
                    model="gemini-2.5-flash",
                    contents=[
                        types.Content(parts=[
                            types.Part(text="Transcribe this audio to text. Only return the transcribed text, no other commentary."),
                            types.Part(inline_data=types.Blob(data=wav_data, mime_type="audio/wav"))
                        ])
                    ]
                )
                
                if response.text:
                    transcription_result = TranscriptionResult(
                        text=response.text.strip(),
                        is_final=True,
                        confidence=0.8,  # Estimated confidence
                        timestamp=time.time(),
                        language=self.source_language
                    )
                    
                    if self.transcription_callback:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.transcription_callback, transcription_result
                        )
                
                # Clear processed audio
                self.accumulated_audio.clear()
                
            except Exception as e:
                logger.error(f"Error in fallback transcription: {e}")
                
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the transcription buffer"""
        if self.is_streaming:
            self.accumulated_audio.extend(audio_data)
            
            # Keep only last 10 seconds to prevent memory issues
            max_samples = self.sample_rate * 2 * 10  # 10 seconds of 16-bit audio
            if len(self.accumulated_audio) > max_samples:
                self.accumulated_audio = self.accumulated_audio[-max_samples:]
                
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        self.is_streaming = False
        self.accumulated_audio.clear()
        logger.info("Stopped fallback streaming speech-to-text transcription") 