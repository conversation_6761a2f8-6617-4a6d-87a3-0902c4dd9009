#!/usr/bin/env python3
"""
Test script for the restructured Omnispeak backend

This script tests that the restructured application works correctly
by testing imports, API endpoints, and basic functionality.
"""
import sys
import asyncio
import json
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test that all imports work correctly"""
    print("🧪 Testing imports...")
    
    try:
        # Test config
        from src.utils.config import INPUT_SAMPLE_RATE, OUTPUT_SAMPLE_RATE
        print(f"  ✅ Config: {INPUT_SAMPLE_RATE}Hz -> {OUTPUT_SAMPLE_RATE}Hz")
        
        # Test models
        from src.api.models import Host, Room, TranslationSession
        print("  ✅ Models: Database models")
        
        # Test services
        from src.services.speech.stt import GladiaSTT
        from src.services.speech.tts import TextToSpeech
        from src.services.translation.translator import TextTranslator
        print("  ✅ Services: Speech and translation")
        
        # Test core
        from src.core.pipeline import LiveTransla<PERSON><PERSON><PERSON>eline, PipelineConfig
        from src.core.session import SessionManager
        print("  ✅ Core: Pipeline and session management")
        
        # Test utils
        from src.utils.buffer import buffer_manager
        from src.utils.chat import ChatDatabase
        print("  ✅ Utils: Buffer and chat")
        
        # Test main app
        from src.api.main import app
        print(f"  ✅ FastAPI app: {app.title} v{app.version}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        return False

def test_main_entry_point():
    """Test the main entry point"""
    print("\n🧪 Testing main entry point...")
    
    try:
        from main import app
        print(f"  ✅ Main entry point: {app.title}")
        return True
    except Exception as e:
        print(f"  ❌ Main entry point failed: {e}")
        return False

def test_fastapi_app():
    """Test FastAPI app configuration"""
    print("\n🧪 Testing FastAPI app...")
    
    try:
        from src.api.main import app
        
        # Check basic app properties
        assert app.title == "Omnispeak Backend"
        assert app.version == "0.1.0"
        print("  ✅ App configuration correct")
        
        # Check routes exist
        routes = [route.path for route in app.routes]
        expected_routes = ["/health", "/chat", "/api/pipeline/stats", "/api/pipeline/sessions", "/ws/{target_lang}"]
        
        for expected_route in expected_routes:
            if any(expected_route.replace("{target_lang}", "{target_lang}") in route for route in routes):
                print(f"  ✅ Route exists: {expected_route}")
            else:
                print(f"  ⚠️  Route might be missing: {expected_route}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ FastAPI app test failed: {e}")
        return False

def test_pipeline_config():
    """Test pipeline configuration"""
    print("\n🧪 Testing pipeline configuration...")
    
    try:
        from src.core.pipeline import PipelineConfig
        
        # Create a test config
        config = PipelineConfig(
            target_language="en",
            source_language="ru-RU",
            use_gladia_stt=True,
            use_google_tts=True
        )
        
        assert config.target_language == "en"
        assert config.source_language == "ru-RU"
        assert config.use_gladia_stt == True
        print("  ✅ Pipeline configuration working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Pipeline config test failed: {e}")
        return False

def test_session_manager():
    """Test session manager"""
    print("\n🧪 Testing session manager...")
    
    try:
        from src.core.session import SessionManager
        
        # Create session manager
        manager = SessionManager()
        
        # Create a test session
        session = manager.create_session("test-123", "en", "uk-UA")
        assert session.session_id == "test-123"
        assert session.target_language == "en"
        print("  ✅ Session creation working")
        
        # Test session stats
        manager.record_audio_chunk("test-123", 1024)
        manager.record_transcription("test-123")
        
        session = manager.get_session("test-123")
        assert session.audio_chunks_processed == 1
        assert session.transcriptions_count == 1
        print("  ✅ Session statistics working")
        
        # Clean up
        manager.end_session("test-123")
        print("  ✅ Session cleanup working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Session manager test failed: {e}")
        return False

async def test_websocket_endpoint():
    """Test WebSocket endpoint (basic structure test)"""
    print("\n🧪 Testing WebSocket endpoint structure...")
    
    try:
        from src.api.websocket import WebSocketCallbacks, handle_json_message
        
        # Test callback class structure
        class MockWebSocket:
            async def send_json(self, data):
                pass
            async def send_bytes(self, data):
                pass
        
        callbacks = WebSocketCallbacks(MockWebSocket(), "test-session")
        assert callbacks.session_id == "test-session"
        print("  ✅ WebSocket callbacks structure working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ WebSocket test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Restructured Omnispeak Backend\n")
    
    tests = [
        test_imports,
        test_main_entry_point,
        test_fastapi_app,
        test_pipeline_config,
        test_session_manager,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    # Run async test
    try:
        asyncio.run(test_websocket_endpoint())
        passed += 1
        total += 1
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        total += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The restructured codebase is working perfectly.")
        print("\n✅ Your app is ready to use with the new clean structure!")
        print("\n🚀 To start the server:")
        print("   uvicorn main:app --reload")
        print("\n🔗 WebSocket endpoint:")
        print("   ws://localhost:8000/ws/{target_language}")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
