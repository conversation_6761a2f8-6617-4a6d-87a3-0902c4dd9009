"""
Sentence detection and buffering system for live translation
"""
import asyncio
import logging
import re
import time
from typing import List, Optional, Callable, Deque
from collections import deque
from dataclasses import dataclass
from src.services.speech.stt import TranscriptionResult

logger = logging.getLogger(__name__)

@dataclass
class Sentence:
    text: str
    timestamp: float
    confidence: float
    language: str
    is_complete: bool = True

@dataclass
class TranslationContext:
    previous_sentences: List[Sentence]
    current_sentence: Sentence
    max_context_sentences: int = 3

class SentenceBuffer:
    """
    Manages sentence detection, buffering, and context for translation
    """

    def __init__(self, max_context_sentences: int = 5, sentence_timeout: float = 1.5, source_lang: str = "ru"):  # Increased from 3 to 5
        self.max_context_sentences = max_context_sentences
        self.sentence_timeout = sentence_timeout
        self.source_lang = source_lang

        # Buffers
        self.current_text = ""
        self.interim_text = ""
        self.completed_sentences: Deque[Sentence] = deque(maxlen=max_context_sentences * 2)  # Keep more history
        self.pending_sentence_start_time = None
        
        # Enhanced context tracking
        self.conversation_flow = []  # Track conversation themes and transitions
        self.speaker_style = {}  # Track speaking patterns for consistency
        self.topic_transitions = []  # Track when topics change
        
        # Callbacks
        self.sentence_ready_callback: Optional[Callable[[TranslationContext], None]] = None
        
        # Enhanced sentence detection patterns
        self.sentence_end_pattern = re.compile(r'[.!?]+\s*')
        self.abbreviation_pattern = re.compile(r'\b(?:Mr|Mrs|Ms|Dr|Prof|Sr|Jr|vs|etc|i\.e|e\.g)\.\s*', re.IGNORECASE)
        
        # Ukrainian-specific patterns
        self.ukrainian_abbreviations = re.compile(r'\b(?:проф|др|вул|пл|бул|пр)\.\s*', re.IGNORECASE)
        
        # Timing
        self.last_activity_time = time.time()
        
        # Start timeout checker
        asyncio.create_task(self._timeout_checker())
        
    def set_sentence_ready_callback(self, callback: Callable[[TranslationContext], None]):
        """Set callback function to receive completed sentences with context"""
        self.sentence_ready_callback = callback
        
    async def add_transcription(self, transcription: TranscriptionResult) -> None:
        """Add new transcription result to the buffer"""
        self.last_activity_time = time.time()
        
        if transcription.is_final:
            # Final transcription - update current text
            self.current_text = transcription.text.strip()
            self.interim_text = ""
            
            # Check if we have complete sentences
            await self._process_final_text(transcription)
        else:
            # Interim transcription - store temporarily and potentially emit
            self.interim_text = transcription.text.strip()
            
            logger.info(f"[INTERIM-DEBUG] Received interim text: '{self.interim_text}'")
            
            # If we haven't started a sentence timer yet, start it for ANY content
            if self.pending_sentence_start_time is None and self.interim_text:
                self.pending_sentence_start_time = time.time()
                logger.info(f"[INTERIM-DEBUG] Started timeout timer for interim text")
            
            # For live streams: if interim text is substantially different from current text,
            # and we have meaningful content, consider emitting it
            if (self.interim_text and 
                len(self.interim_text.split()) >= 2 and  # At least 2 words
                self.interim_text != self.current_text):
                
                # Check if we should emit the previous content first
                if (self.current_text and 
                    self.current_text not in self.interim_text and
                    len(self.current_text.split()) >= 1):
                    
                    logger.info(f"[INTERIM-DEBUG] Emitting previous text before new interim: '{self.current_text}'")
                    await self._emit_sentence(self.current_text, transcription)
                
                # Update current text to interim text for potential future emission
                self.current_text = self.interim_text
    
    async def _process_final_text(self, transcription: TranscriptionResult) -> None:
        """Process final transcription text and extract sentences"""
        if not self.current_text:
            return
            
        logger.info(f"[SENTENCE-DEBUG] Processing final text: '{self.current_text}'")
        
        # Split text into potential sentences
        sentences = self._extract_sentences(self.current_text)
        
        logger.info(f"[SENTENCE-DEBUG] Extracted sentences: {sentences}")
        
        if sentences:
            # Process each complete sentence
            for sentence_text in sentences[:-1]:  # All but the last
                if sentence_text.strip():
                    logger.info(f"[SENTENCE-DEBUG] Emitting sentence: '{sentence_text.strip()}'")
                    await self._emit_sentence(sentence_text.strip(), transcription)
            
            # Check if the last part is a complete sentence
            last_part = sentences[-1].strip()
            is_complete = self._is_complete_sentence(last_part)
            logger.info(f"[SENTENCE-DEBUG] Last part: '{last_part}', is_complete: {is_complete}")
            
            if last_part and is_complete:
                logger.info(f"[SENTENCE-DEBUG] Emitting final sentence: '{last_part}'")
                await self._emit_sentence(last_part, transcription)
                self.current_text = ""
            else:
                # Keep the incomplete part for next time
                self.current_text = last_part
                logger.info(f"[SENTENCE-DEBUG] Keeping incomplete text: '{last_part}'")
        else:
            # No sentence boundaries found - check if the whole text is a complete sentence
            logger.info(f"[SENTENCE-DEBUG] No sentence boundaries found, checking if whole text is complete")
            if self._is_complete_sentence(self.current_text):
                logger.info(f"[SENTENCE-DEBUG] Whole text is complete sentence: '{self.current_text}'")
                await self._emit_sentence(self.current_text, transcription)
                self.current_text = ""
            else:
                # Check timeout
                if self.pending_sentence_start_time is None:
                    self.pending_sentence_start_time = time.time()
                    logger.info(f"[SENTENCE-DEBUG] Started sentence timeout timer")
    
    def _extract_sentences(self, text: str) -> List[str]:
        """Extract sentences from text, handling abbreviations"""
        # First, protect abbreviations
        protected_text = text
        abbreviations = list(self.abbreviation_pattern.finditer(text))
        
        # Replace abbreviation periods temporarily
        for match in reversed(abbreviations):
            start, end = match.span()
            protected_text = protected_text[:end-1] + "@@PERIOD@@" + protected_text[end:]
        
        # Split on sentence endings
        parts = self.sentence_end_pattern.split(protected_text)
        
        # Restore abbreviation periods
        restored_parts = []
        for part in parts:
            restored_part = part.replace("@@PERIOD@@", ".")
            restored_parts.append(restored_part)
        
        return [part for part in restored_parts if part.strip()]
    
    def _is_complete_sentence(self, text: str) -> bool:
        """Check if text appears to be a complete sentence"""
        text = text.strip()
        if not text:
            return False
            
        # Check for sentence ending punctuation
        if re.search(r'[.!?]+$', text):
            return True
            
        # Check for minimum length and some heuristics
        if len(text) < 2:  # Lowered threshold for Ukrainian single words
            return False
            
        # Check if it looks like a complete thought (has subject and verb indicators)
        words = text.lower().split()
        
        # For Ukrainian and other languages: be more lenient with sentence detection
        # Single meaningful words can be complete sentences (e.g., "дякую" = "thank you")
        if len(words) >= 1:
            # Ukrainian common complete sentence words
            ukrainian_complete_words = [
                'дякую', 'так', 'ні', 'привіт', 'бувай', 'вибачте', 'будь ласка',
                'добре', 'погано', 'гаразд', 'звичайно', 'можливо', 'ймовірно'
            ]
            
            # English common complete sentence words  
            english_complete_words = [
                'yes', 'no', 'hello', 'goodbye', 'thanks', 'please', 'sorry',
                'okay', 'good', 'bad', 'fine', 'sure', 'maybe', 'probably'
            ]
            
            # Check if it's a known complete word
            if any(word in ukrainian_complete_words or word in english_complete_words for word in words):
                return True
                
            # Relaxed heuristic: if it has reasonable length and no trailing comma/conjunction
            if len(words) >= 2:
                ukrainian_conjunctions = ['і', 'та', 'але', 'або', 'щоб', 'якщо']
                english_conjunctions = ['and', 'or', 'but', 'so', 'yet', 'if', 'when']
                
                if not text.endswith(',') and not any(text.lower().endswith(conj) for conj in ukrainian_conjunctions + english_conjunctions):
                    return True
        
        return False
    
    async def _emit_sentence(self, sentence_text: str, transcription: TranscriptionResult) -> None:
        """Emit a completed sentence with enhanced context"""
        sentence = Sentence(
            text=sentence_text,
            timestamp=transcription.timestamp,
            confidence=transcription.confidence,
            language=transcription.language,
            is_complete=True
        )
        
        # Add to completed sentences buffer
        self.completed_sentences.append(sentence)
        
        # Analyze conversation flow and context quality
        context_sentences = self._select_optimal_context()
        
        # Enhanced context creation with conversation flow analysis
        context = TranslationContext(
            previous_sentences=context_sentences,
            current_sentence=sentence,
            max_context_sentences=self.max_context_sentences
        )
        
        # Track conversation patterns for better future context
        self._update_conversation_tracking(sentence, context_sentences)
        
        # Emit to callback
        if self.sentence_ready_callback:
            # Check if callback is async
            if asyncio.iscoroutinefunction(self.sentence_ready_callback):
                # Async callback - call directly
                await self.sentence_ready_callback(context)
            else:
                # Sync callback - use executor
                await asyncio.get_event_loop().run_in_executor(
                    None, self.sentence_ready_callback, context
                )
        
        # Reset pending sentence timer
        self.pending_sentence_start_time = None
        
        # Enhanced logging with context information
        context_info = f"with {len(context_sentences)} context sentences"
        if context_sentences:
            recent_context = " | ".join([s.text[:30] + "..." if len(s.text) > 30 else s.text for s in context_sentences[-2:]])
            context_info += f" (recent: {recent_context})"
        
        logger.info(f"Emitted sentence: '{sentence_text}' {context_info}")
    
    def _select_optimal_context(self) -> List[Sentence]:
        """Select the most relevant context sentences for translation"""
        if not self.completed_sentences:
            return []
        
        # Get recent sentences up to max limit
        available_sentences = list(self.completed_sentences)
        if len(available_sentences) <= self.max_context_sentences:
            return available_sentences[:-1] if available_sentences else []  # Exclude current sentence
        
        # Advanced context selection - prioritize recent and topically relevant sentences
        recent_sentences = available_sentences[-self.max_context_sentences-1:-1]  # Exclude current sentence
        
        # For now, return recent sentences, but this could be enhanced with:
        # - Topic relevance scoring
        # - Speaker turn detection
        # - Semantic similarity
        return recent_sentences
    
    def _update_conversation_tracking(self, current_sentence: Sentence, context_sentences: List[Sentence]) -> None:
        """Update conversation flow tracking for better future context selection"""
        # Track basic conversation patterns
        self.conversation_flow.append({
            'timestamp': current_sentence.timestamp,
            'sentence_length': len(current_sentence.text),
            'confidence': current_sentence.confidence,
            'context_count': len(context_sentences)
        })
        
        # Keep conversation flow manageable
        if len(self.conversation_flow) > 50:
            self.conversation_flow = self.conversation_flow[-30:]  # Keep last 30 entries
        
        # Simple topic transition detection (could be enhanced)
        if len(context_sentences) >= 2:
            # Very basic topic change detection based on vocabulary overlap
            current_words = set(current_sentence.text.lower().split())
            recent_words = set()
            for sent in context_sentences[-2:]:
                recent_words.update(sent.text.lower().split())
            
            overlap_ratio = len(current_words.intersection(recent_words)) / max(len(current_words), 1)
            
            if overlap_ratio < 0.2:  # Low overlap might indicate topic change
                self.topic_transitions.append({
                    'timestamp': current_sentence.timestamp,
                    'overlap_ratio': overlap_ratio
                })
                
                # Keep topic transitions manageable
                if len(self.topic_transitions) > 10:
                    self.topic_transitions = self.topic_transitions[-5:]
    
    async def _timeout_checker(self) -> None:
        """Check for sentence timeouts and emit incomplete sentences"""
        while True:
            await asyncio.sleep(1.0)  # Check every second
            
            current_time = time.time()
            
            # Check if we have pending content that should be emitted
            if (self.pending_sentence_start_time is not None and 
                current_time - self.pending_sentence_start_time > self.sentence_timeout):
                
                # Check if we have accumulated content
                content_to_emit = self.current_text or self.interim_text
                if content_to_emit.strip():
                    logger.info(f"[TIMEOUT] Emitting incomplete sentence after {self.sentence_timeout}s timeout: '{content_to_emit}'")
                    
                    # Create a synthetic transcription result for timeout case
                    timeout_transcription = TranscriptionResult(
                        text=content_to_emit,
                        is_final=True,
                        confidence=0.5,  # Lower confidence for timeout cases
                        timestamp=current_time,
                        language=self.source_lang
                    )
                    
                    await self._emit_sentence(content_to_emit.strip(), timeout_transcription)
                    
                    # Clear the content
                    self.current_text = ""
                    self.interim_text = ""
                    self.pending_sentence_start_time = None
    
    async def force_flush(self) -> None:
        """Force emit any pending text as a sentence"""
        if self.current_text.strip():
            sentence = Sentence(
                text=self.current_text.strip(),
                timestamp=time.time(),
                confidence=0.8,
                language="auto",
                is_complete=False
            )
            
            # Add to completed sentences buffer
            self.completed_sentences.append(sentence)
            
            # Create translation context
            context = TranslationContext(
                previous_sentences=list(self.completed_sentences)[-self.max_context_sentences-1:-1],
                current_sentence=sentence,
                max_context_sentences=self.max_context_sentences
            )
            
            # Emit to callback
            if self.sentence_ready_callback:
                # Check if callback is async
                if asyncio.iscoroutinefunction(self.sentence_ready_callback):
                    # Async callback - call directly
                    await self.sentence_ready_callback(context)
                else:
                    # Sync callback - use executor
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.sentence_ready_callback, context
                    )
            
            logger.info(f"Force-flushed sentence: '{self.current_text.strip()}'")
            
            # Clear current text and reset timer
            self.current_text = ""
            self.pending_sentence_start_time = None
    
    def get_recent_context(self, max_sentences: int = None) -> List[Sentence]:
        """Get recent sentences for context"""
        if max_sentences is None:
            max_sentences = self.max_context_sentences
        return list(self.completed_sentences)[-max_sentences:]
    
    def clear_buffer(self) -> None:
        """Clear all buffers"""
        self.current_text = ""
        self.interim_text = ""
        self.completed_sentences.clear()
        self.pending_sentence_start_time = None
        logger.info("Sentence buffer cleared") 