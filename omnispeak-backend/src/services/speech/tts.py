"""
Text-to-Speech module for live translation
"""
import asyncio
import logging
import io
import wave
import time
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass
from google.cloud import texttospeech
from google import genai
from google.genai import types
from src.utils import config
from src.services.translation.translator import TranslationResult

logger = logging.getLogger(__name__)

@dataclass
class AudioResult:
    audio_data: bytes
    sample_rate: int
    format: str
    duration_ms: float
    text: str
    language: str
    timestamp: float

class TextToSpeech:
    """
    Converts translated text to speech using Google Cloud Text-to-Speech
    """
    
    def __init__(self, target_language: str, voice_gender: str = "NEUTRAL", speaking_rate: float = 1.0):
        self.target_language = target_language
        self.voice_gender = voice_gender
        self.speaking_rate = speaking_rate
        
        try:
            self.client = texttospeech.TextToSpeechClient()
            self.is_available = True
            logger.info("Google Cloud TTS client initialized successfully")
        except Exception as e:
            logger.warning(f"Google Cloud TTS not available: {e}")
            self.client = None
            self.is_available = False
            
        self.audio_callback: Optional[Callable[[AudioResult], None]] = None
        self.is_active = True
        
        # Voice configuration cache
        self._voice_config = None
        self._audio_config = None
        self._setup_voice_config()
        
    def _setup_voice_config(self):
        """Setup voice and audio configuration"""
        if not self.is_available:
            return
            
        # Language code mapping
        language_codes = {
            "en": "en-US",
            "uk": "uk-UA",
            "de": "de-DE", 
            "fr": "fr-FR",
            "es": "es-ES",
            "it": "it-IT",
            "pt": "pt-BR",
            "ru": "ru-RU",
            "ja": "ja-JP",
            "ko": "ko-KR",
            "zh": "zh-CN",
            "ar": "ar-XA"
        }
        
        language_code = language_codes.get(self.target_language, "en-US")
        
        # Gender mapping
        gender_map = {
            "NEUTRAL": texttospeech.SsmlVoiceGender.NEUTRAL,
            "MALE": texttospeech.SsmlVoiceGender.MALE,
            "FEMALE": texttospeech.SsmlVoiceGender.FEMALE
        }
        
        gender = gender_map.get(self.voice_gender, texttospeech.SsmlVoiceGender.NEUTRAL)
        
        # Voice selection
        self._voice_config = texttospeech.VoiceSelectionParams(
            language_code=language_code,
            ssml_gender=gender,
        )
        
        # Audio configuration
        self._audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.LINEAR16,
            sample_rate_hertz=config.OUTPUT_SAMPLE_RATE,
            speaking_rate=self.speaking_rate,
            pitch=0.0,
            volume_gain_db=0.0,
        )
        
    def set_audio_callback(self, callback: Callable[[AudioResult], None]):
        """Set callback function to receive audio results"""
        self.audio_callback = callback
        
    async def convert_to_speech(self, translation_result: TranslationResult) -> None:
        """Convert translation result to speech"""
        if not self.is_active or not translation_result.translated_text.strip():
            return
            
        try:
            start_time = time.time()
            
            logger.info(f"Converting to speech: '{translation_result.translated_text}'")
            
            if self.is_available:
                audio_data = await self._google_tts(translation_result.translated_text)
            else:
                audio_data = await self._fallback_tts(translation_result.translated_text)
                
            if audio_data:
                # Calculate duration
                duration_ms = len(audio_data) / (config.OUTPUT_SAMPLE_RATE * 2) * 1000  # 16-bit audio
                
                audio_result = AudioResult(
                    audio_data=audio_data,
                    sample_rate=config.OUTPUT_SAMPLE_RATE,
                    format="LINEAR16",
                    duration_ms=duration_ms,
                    text=translation_result.translated_text,
                    language=translation_result.target_language,
                    timestamp=time.time()
                )
                
                # Call callback
                if self.audio_callback:
                    # Check if callback is async
                    if asyncio.iscoroutinefunction(self.audio_callback):
                        # Async callback - call directly
                        await self.audio_callback(audio_result)
                    else:
                        # Sync callback - use executor
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.audio_callback, audio_result
                        )
                
                elapsed_time = time.time() - start_time
                logger.info(f"TTS completed in {elapsed_time:.2f}s, audio duration: {duration_ms:.0f}ms")
            else:
                logger.warning("No audio data generated from TTS")
                
        except Exception as e:
            logger.error(f"Error in text-to-speech conversion: {e}")
            
    async def _google_tts(self, text: str) -> Optional[bytes]:
        """Use Google Cloud TTS"""
        try:
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.synthesize_speech(
                    input=synthesis_input,
                    voice=self._voice_config,
                    audio_config=self._audio_config
                )
            )
            
            return response.audio_content
            
        except Exception as e:
            logger.error(f"Google TTS error: {e}")
            return None
            
    async def _fallback_tts(self, text: str) -> Optional[bytes]:
        """Fallback TTS using Gemini"""
        try:
            # Note: Gemini doesn't directly support TTS, so this is a placeholder
            # In a real implementation, you might use another TTS service
            logger.warning("Fallback TTS not fully implemented - generating silence")
            
            # Generate a short silence as placeholder
            duration_seconds = max(0.5, len(text) * 0.1)  # Rough estimation
            samples = int(config.OUTPUT_SAMPLE_RATE * duration_seconds)
            
            # Create silent audio data
            silence_data = b'\x00\x00' * samples  # 16-bit silence
            
            return silence_data
            
        except Exception as e:
            logger.error(f"Fallback TTS error: {e}")
            return None
    
    def stop(self):
        """Stop the TTS engine"""
        self.is_active = False
        logger.info("Text-to-speech stopped")

class StreamingTextToSpeech:
    """
    Streaming TTS that can handle multiple concurrent requests efficiently
    """
    
    def __init__(self, target_language: str, max_concurrent: int = 3):
        self.target_language = target_language
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Create multiple TTS instances for better throughput
        self.tts_instances = []
        for i in range(max_concurrent):
            tts = TextToSpeech(target_language)
            self.tts_instances.append(tts)
            
        self.audio_callback: Optional[Callable[[AudioResult], None]] = None
        self.is_active = True
        
        # Queue for managing requests
        self.request_queue = asyncio.Queue()
        self.worker_tasks = []
        
        # Start worker tasks
        for i in range(max_concurrent):
            task = asyncio.create_task(self._worker(i))
            self.worker_tasks.append(task)
            
    def set_audio_callback(self, callback: Callable[[AudioResult], None]):
        """Set callback function to receive audio results"""
        self.audio_callback = callback
        for tts in self.tts_instances:
            tts.set_audio_callback(callback)
            
    async def convert_to_speech(self, translation_result: TranslationResult) -> None:
        """Queue translation result for TTS conversion"""
        if self.is_active:
            await self.request_queue.put(translation_result)
            
    async def _worker(self, worker_id: int) -> None:
        """Worker task for processing TTS requests"""
        tts = self.tts_instances[worker_id]
        
        while self.is_active:
            try:
                # Get next request with timeout
                translation_result = await asyncio.wait_for(
                    self.request_queue.get(), timeout=1.0
                )
                
                # Process the request
                await tts.convert_to_speech(translation_result)
                
            except asyncio.TimeoutError:
                # Continue waiting for requests
                continue
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
                
    async def stop(self):
        """Stop all TTS workers"""
        self.is_active = False
        
        # Stop all TTS instances
        for tts in self.tts_instances:
            tts.stop()
            
        # Cancel worker tasks
        for task in self.worker_tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        logger.info("Streaming TTS stopped")

class AdaptiveTextToSpeech:
    """
    Adaptive TTS that adjusts voice parameters based on content and context
    """
    
    def __init__(self, target_language: str):
        self.target_language = target_language
        self.base_tts = TextToSpeech(target_language)
        self.audio_callback: Optional[Callable[[AudioResult], None]] = None
        
        # Adaptation parameters
        self.current_speaking_rate = 1.0
        self.recent_translations = []
        self.max_history = 10
        
    def set_audio_callback(self, callback: Callable[[AudioResult], None]):
        """Set callback function to receive audio results"""
        self.audio_callback = callback
        self.base_tts.set_audio_callback(callback)
        
    async def convert_to_speech(self, translation_result: TranslationResult) -> None:
        """Convert with adaptive parameters"""
        # Store recent translation for adaptation
        self.recent_translations.append(translation_result)
        if len(self.recent_translations) > self.max_history:
            self.recent_translations.pop(0)
            
        # Adapt speaking rate based on confidence and complexity
        adapted_rate = self._calculate_speaking_rate(translation_result)
        
        # Update TTS configuration if needed
        if abs(adapted_rate - self.current_speaking_rate) > 0.1:
            self.current_speaking_rate = adapted_rate
            self.base_tts.speaking_rate = adapted_rate
            self.base_tts._setup_voice_config()
            logger.info(f"Adapted speaking rate to {adapted_rate:.2f}")
            
        # Convert to speech
        await self.base_tts.convert_to_speech(translation_result)
        
    def _calculate_speaking_rate(self, translation_result: TranslationResult) -> float:
        """Calculate optimal speaking rate based on translation characteristics"""
        base_rate = 1.0
        
        # Adjust based on confidence
        confidence_factor = translation_result.confidence
        if confidence_factor < 0.7:
            base_rate *= 0.9  # Slow down for low confidence
        elif confidence_factor > 0.9:
            base_rate *= 1.1  # Speed up for high confidence
            
        # Adjust based on text complexity (rough heuristic)
        text_length = len(translation_result.translated_text)
        if text_length > 100:
            base_rate *= 0.95  # Slow down for long texts
        elif text_length < 20:
            base_rate *= 1.05  # Speed up for short texts
            
        # Adjust based on recent pattern
        if len(self.recent_translations) >= 3:
            avg_confidence = sum(t.confidence for t in self.recent_translations[-3:]) / 3
            if avg_confidence < 0.6:
                base_rate *= 0.9  # Overall slower if recent translations are uncertain
                
        # Clamp to reasonable range
        return max(0.7, min(1.4, base_rate))
        
    def stop(self):
        """Stop the adaptive TTS"""
        self.base_tts.stop()

class AudioBuffer:
    """
    Manages audio output buffering and streaming
    """
    
    def __init__(self, buffer_size_ms: int = 500):
        self.buffer_size_ms = buffer_size_ms
        self.audio_queue = asyncio.Queue()
        self.is_playing = False
        self.is_active = True
        
        # Start playback task
        self.playback_task = asyncio.create_task(self._playback_loop())
        
    async def add_audio(self, audio_result: AudioResult) -> None:
        """Add audio to the buffer"""
        if self.is_active:
            await self.audio_queue.put(audio_result)
            
    async def _playback_loop(self) -> None:
        """Main playback loop"""
        while self.is_active:
            try:
                # Get next audio with timeout
                audio_result = await asyncio.wait_for(
                    self.audio_queue.get(), timeout=1.0
                )
                
                # Simulate audio playback (in real implementation, this would
                # stream to audio output device)
                logger.info(f"Playing audio: {audio_result.duration_ms:.0f}ms for '{audio_result.text[:50]}...'")
                
                # Simulate playback time
                await asyncio.sleep(audio_result.duration_ms / 1000.0)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Playback error: {e}")
                
    async def stop(self):
        """Stop the audio buffer"""
        self.is_active = False
        self.playback_task.cancel()
        try:
            await self.playback_task
        except asyncio.CancelledError:
            pass
        logger.info("Audio buffer stopped") 