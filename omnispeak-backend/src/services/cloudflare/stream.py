"""
Module for Cloudflare Stream API integration
"""
import asyncio
import aiohttp
import logging
from typing import Dict, Optional, Any
from pathlib import Path
from src.utils import config

logger = logging.getLogger(__name__)

class CloudflareStreamClient:
    """Client for working with Cloudflare Stream API"""

    def __init__(self, api_token: str = None, account_id: str = None):
        self.api_token = api_token or config.CLOUDFLARE_API_TOKEN
        self.account_id = account_id or config.CLOUDFLARE_ACCOUNT_ID
        self.base_url = f"{config.CLOUDFLARE_STREAM_API_BASE}/accounts/{self.account_id}/stream"

        if not self.api_token or not self.account_id:
            raise ValueError("Cloudflare API token and account ID must be set")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
    
    async def create_live_input(self, name: str, recording_mode: str = "automatic") -> Dict[str, Any]:
        """
        Creates live input for streaming

        Args:
            name: Stream name
            recording_mode: Recording mode ("automatic", "off")

        Returns:
            Dictionary with created live input information
        """
        url = f"{self.base_url}/live_inputs"
        
        payload = {
            "meta": {"name": name},
            "recording": {
                "mode": recording_mode,
                "requireSignedURLs": False,
                "allowedOrigins": None
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.headers, json=payload) as response:
                    if response.status == 201:
                        data = await response.json()
                        logger.info(f"Created live input: {data['result']['uid']}")
                        return data['result']
                    else:
                        error_text = await response.text()
                        logger.error(f"Error creating live input: {response.status} - {error_text}")
                        raise Exception(f"Failed to create live input: {response.status}")
        except Exception as e:
            logger.error(f"Error creating live input: {e}")
            raise
    
    async def upload_video(self, video_path: Path, name: str = None) -> Dict[str, Any]:
        """
        Uploads video file to Cloudflare Stream

        Args:
            video_path: Path to video file
            name: Video name (optional)

        Returns:
            Dictionary with uploaded video information
        """
        url = f"{self.base_url}"

        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")

        # Prepare headers for multipart/form-data
        upload_headers = {
            "Authorization": f"Bearer {self.api_token}"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                with open(video_path, 'rb') as video_file:
                    data = aiohttp.FormData()
                    data.add_field('file', video_file, filename=video_path.name)
                    
                    if name:
                        data.add_field('meta', f'{{"name": "{name}"}}')
                    
                    async with session.post(url, headers=upload_headers, data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.info(f"Video uploaded: {result['result']['uid']}")
                            return result['result']
                        else:
                            error_text = await response.text()
                            logger.error(f"Error uploading video: {response.status} - {error_text}")
                            raise Exception(f"Failed to upload video: {response.status}")
        except Exception as e:
            logger.error(f"Error uploading video: {e}")
            raise
    
    async def get_video_status(self, video_uid: str) -> Dict[str, Any]:
        """
        Gets video status

        Args:
            video_uid: Video UID

        Returns:
            Dictionary with video status information
        """
        url = f"{self.base_url}/{video_uid}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data['result']
                    else:
                        error_text = await response.text()
                        logger.error(f"Error getting video status: {response.status} - {error_text}")
                        raise Exception(f"Failed to get video status: {response.status}")
        except Exception as e:
            logger.error(f"Error getting video status: {e}")
            raise

    async def delete_video(self, video_uid: str) -> bool:
        """
        Deletes video

        Args:
            video_uid: Video UID

        Returns:
            True if deletion successful
        """
        url = f"{self.base_url}/{video_uid}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.delete(url, headers=self.headers) as response:
                    if response.status == 200:
                        logger.info(f"Video deleted: {video_uid}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Error deleting video: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"Error deleting video: {e}")
            return False
