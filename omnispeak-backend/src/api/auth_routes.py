"""
Authentication Routes

FastAPI routes for user authentication, registration, and profile management.
"""
import os
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Response, UploadFile, File
from fastapi.responses import JSONResponse

from src.auth.models import (
    <PERSON>r<PERSON><PERSON><PERSON>, <PERSON>rLog<PERSON>, UserProfile, UserProfileUpdate, 
    Guest<PERSON><PERSON>n, AuthResponse, User
)
from src.auth.service import AuthService
from src.auth.dependencies import (
    get_auth_service, get_current_host, get_current_guest, 
    get_current_user_optional, get_current_user_any
)
from src.services.cloudflare.stream import CloudflareStreamClient
from src.utils.config import C<PERSON><PERSON>UDFLARE_API_TOKEN, CLOUDFLARE_ACCOUNT_ID

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/register", response_model=AuthResponse)
async def register_host(
    user_data: UserRegister,
    response: Response,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Register a new host user"""
    try:
        user, session_token = auth_service.register_host(user_data)
        
        # Set secure cookie
        response.set_cookie(
            key="session_token",
            value=session_token,
            httponly=True,
            secure=True,
            samesite="lax",
            max_age=7 * 24 * 60 * 60  # 7 days
        )
        
        return AuthResponse(
            success=True,
            message="Registration successful",
            user=UserProfile(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                preferred_language=user.preferred_language,
                original_language=user.original_language,
                voice_sample_url=user.voice_sample_url,
                created_at=user.created_at
            ),
            session_token=session_token
        )
    
    except HTTPException as e:
        return AuthResponse(
            success=False,
            message=e.detail
        )


@router.post("/login", response_model=AuthResponse)
async def login_host(
    login_data: UserLogin,
    response: Response,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Login host user"""
    try:
        user, session_token = auth_service.login_host(login_data)
        
        # Set secure cookie
        response.set_cookie(
            key="session_token",
            value=session_token,
            httponly=True,
            secure=True,
            samesite="lax",
            max_age=7 * 24 * 60 * 60  # 7 days
        )
        
        return AuthResponse(
            success=True,
            message="Login successful",
            user=UserProfile(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                preferred_language=user.preferred_language,
                original_language=user.original_language,
                voice_sample_url=user.voice_sample_url,
                created_at=user.created_at
            ),
            session_token=session_token
        )
    
    except HTTPException as e:
        return AuthResponse(
            success=False,
            message=e.detail
        )


@router.post("/guest", response_model=AuthResponse)
async def join_as_guest(
    guest_data: GuestJoin,
    response: Response,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Create guest session"""
    session_token = auth_service.create_guest_session(guest_data)
    
    # Set secure cookie
    response.set_cookie(
        key="session_token",
        value=session_token,
        httponly=True,
        secure=True,
        samesite="lax",
        max_age=24 * 60 * 60  # 1 day for guests
    )
    
    return AuthResponse(
        success=True,
        message="Guest session created",
        session_token=session_token
    )


@router.post("/logout")
async def logout(
    response: Response,
    current_user = Depends(get_current_user_optional),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Logout user"""
    if current_user:
        user, session = current_user
        auth_service.logout(session.session_token)
    
    # Clear cookie
    response.delete_cookie(key="session_token")
    
    return {"message": "Logged out successfully"}


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: User = Depends(get_current_host)
):
    """Get current host user profile"""
    return UserProfile(
        id=current_user.id,
        email=current_user.email,
        full_name=current_user.full_name,
        preferred_language=current_user.preferred_language,
        original_language=current_user.original_language,
        voice_sample_url=current_user.voice_sample_url,
        created_at=current_user.created_at
    )


@router.put("/profile", response_model=UserProfile)
async def update_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(get_current_host),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Update host user profile"""
    update_dict = profile_data.dict(exclude_unset=True)
    updated_user = auth_service.update_user_profile(current_user.id, update_dict)
    
    return UserProfile(
        id=updated_user.id,
        email=updated_user.email,
        full_name=updated_user.full_name,
        preferred_language=updated_user.preferred_language,
        original_language=updated_user.original_language,
        voice_sample_url=updated_user.voice_sample_url,
        created_at=updated_user.created_at
    )


@router.post("/voice-sample")
async def upload_voice_sample(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_host),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Upload voice sample for host"""
    if not file.content_type.startswith('audio/'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an audio file"
        )
    
    # Create voice samples directory
    voice_samples_dir = "voice_samples"
    os.makedirs(voice_samples_dir, exist_ok=True)
    
    # Save file locally
    file_path = f"{voice_samples_dir}/user_{current_user.id}_{file.filename}"
    
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Upload to Cloudflare if configured
    cloudflare_url = None
    if CLOUDFLARE_API_TOKEN and CLOUDFLARE_ACCOUNT_ID:
        try:
            cloudflare_client = CloudflareStreamClient()
            upload_result = await cloudflare_client.upload_video(
                file_path, 
                f"voice_sample_user_{current_user.id}"
            )
            cloudflare_url = upload_result.get('preview')
        except Exception as e:
            # Continue without Cloudflare upload
            pass
    
    # Update user profile
    updated_user = auth_service.update_voice_sample(
        current_user.id, 
        file_path, 
        cloudflare_url or f"/voice-samples/{current_user.id}"
    )
    
    return {
        "message": "Voice sample uploaded successfully",
        "voice_sample_url": updated_user.voice_sample_url
    }


@router.get("/status")
async def auth_status(
    current_user = Depends(get_current_user_optional)
):
    """Get current authentication status"""
    if not current_user:
        return {
            "authenticated": False,
            "user_type": None
        }
    
    user, session = current_user
    
    if session.user_type == "host" and user:
        return {
            "authenticated": True,
            "user_type": "host",
            "user": UserProfile(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                preferred_language=user.preferred_language,
                original_language=user.original_language,
                voice_sample_url=user.voice_sample_url,
                created_at=user.created_at
            )
        }
    else:
        return {
            "authenticated": True,
            "user_type": "guest",
            "guest_name": session.guest_name
        }
