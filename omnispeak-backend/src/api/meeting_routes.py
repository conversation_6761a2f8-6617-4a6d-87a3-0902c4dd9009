"""
Meeting Routes

FastAPI routes for meeting management, language streams, and participant handling.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlmodel import Session as DBSession

from src.api.models import (
    MeetingCreate, MeetingUpdate, MeetingLanguageCreate, MeetingJoin,
    MeetingResponse, MeetingLanguageResponse
)
from src.auth.models import User, Session
from src.auth.jwt import create_jwt_for_user, verify_jwt
from src.auth.dependencies import get_current_host, get_current_guest, get_current_user_any
from src.api.database import get_db_session
from src.meetings.service import MeetingService
from src.utils.buffer import buffer_manager
from fastapi import Body


def get_meeting_service(db: DBSession = Depends(get_db_session)) -> MeetingService:
    """Get meeting service instance"""
    return MeetingService(db)

router = APIRouter(prefix="/meetings", tags=["Meetings"])

# --- NEW ENDPOINTS FOR ROOMS & AUTH ---
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uuid


class SpeakerLoginRequest(BaseModel):
    email: str
    password: str


class SpeakerLoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    error: str = None

class RoomJoinRequest(BaseModel):
    room_id: int
    viewer_id: str
    viewer_name: str = None

class RoomLeaveRequest(BaseModel):
    room_id: int
    viewer_id: str

class RoomCreateRequest(BaseModel):
    title: str
    description: str = ""
    is_public: bool = True
    host_language: str = "en"
    jwt: str

class RoomDeleteRequest(BaseModel):
    room_id: int
    jwt: str

class RoomSetLangLimitRequest(BaseModel):
    room_id: int
    jwt: str
    lang_limit: int

class RoomChatRequest(BaseModel):
    room_id: int
    jwt: str = None
    viewer_id: str = None
    viewer_name: str = None
    text: str = None


class UserCreateRequest(BaseModel):
    email: str
    password: str
    jwt: str

class RequestLanguageBody(BaseModel):
    lang_code: str
    stream_name: str = None

############################################################################################################################################

# --- ADD USER VIA API ---
# Tested, working well
@router.post("/auth/add_user")
async def add_user_api(data: UserCreateRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    try:
        payload = verify_jwt(data.jwt)
        creator_user_id = payload.get("user_id")
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid JWT")
    try:
        user_info = meeting_service.add_user(data.email, data.password, creator_user_id)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
    return {"status": "ok", "user": user_info}

############################################################################################################################################

# --- SPEAKER LOGIN ---
# Tested, working good with initial creds
@router.post("/auth/speaker_login", response_model=SpeakerLoginResponse)
async def speaker_login(data: SpeakerLoginRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    user = meeting_service.authenticate_speaker(data.email, data.password)
    if not user:
        error = "Invalid credentials"
        return SpeakerLoginResponse(access_token="", error=error)
    token = create_jwt_for_user(user)
    return SpeakerLoginResponse(access_token=token)

############################################################################################################################################

# --- LIST ROOMS ---
# Tested, working good
@router.get("/rooms/list_rooms")
async def list_rooms(request: Request, meeting_service: MeetingService = Depends(get_meeting_service)):
    # Try to get JWT from Authorization header
    jwt = None
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.lower().startswith("bearer "):
        jwt = auth_header[7:]
    # Or from JSON body
    if not jwt:
        try:
            body = await request.json()
            jwt = body.get("jwt")
        except Exception:
            pass
    user_id = None
    if jwt:
        try:
            payload = verify_jwt(jwt)
            user_id = payload.get("user_id")
        except Exception:
            pass
    public_rooms = meeting_service.get_public_rooms()
    private_rooms = meeting_service.get_private_rooms(user_id) if user_id else []
    return {"public_rooms": public_rooms, "private_rooms": private_rooms}

############################################################################################################################################

# --- ROOM METADATA ---
# Tested, has a vulnerability allowing to read metadata of non-public groups (won't fix)
@router.get("/rooms/metadata/{room_id}")
async def get_room_metadata(room_id: int, meeting_service: MeetingService = Depends(get_meeting_service)):
    room = meeting_service.get_meeting_by_id(room_id)
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    active_langs = meeting_service.get_meeting_languages(room.id)
    lang_urls = {lang.language_code: lang.stream_url for lang in active_langs}
    member_count = meeting_service.get_room_viewer_count(room.id)
    speaker_info = meeting_service.get_room_speaker_info(room.id)
    return {
        "room_id": room.id,
        "title": room.title,
        "active_languages": [lang.language_code for lang in active_langs],
        "language_urls": lang_urls,
        "member_count": member_count,
        "speaker_info": speaker_info,
        "metadata": {
            "description": room.description,
            "is_public": room.is_public,
            "host_language": room.host_language
        }
    }

############################################################################################################################################

# --- JOIN ROOM ---
# Tested, working well
@router.post("/rooms/join_room")
async def join_room(data: RoomJoinRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    room = meeting_service.get_meeting_by_id(data.room_id)
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    meeting_service.add_viewer_to_room(room.id, data.viewer_id, data.viewer_name)
    active_langs = meeting_service.get_meeting_languages(room.id)
    lang_urls = {lang.language_code: lang.stream_url for lang in active_langs}
    member_count = meeting_service.get_room_viewer_count(room.id)
    speaker_info = meeting_service.get_room_speaker_info(room.id)
    return {
        "room_id": room.id,
        "title": room.title,
        "active_languages": [lang.language_code for lang in active_langs],
        "language_urls": lang_urls,
        "member_count": member_count,
        "speaker_info": speaker_info,
        "metadata": {
            "description": room.description,
            "is_public": room.is_public,
            "host_language": room.host_language
        }
    }

############################################################################################################################################

# --- LEAVE ROOM ---
# Tested, working well
@router.post("/rooms/leave_room")
async def leave_room(data: RoomLeaveRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    room = meeting_service.get_meeting_by_id(data.room_id)
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    meeting_service.remove_viewer_from_room(room.id, data.viewer_id)
    return {"status": "ok"}

############################################################################################################################################

# --- CREATE ROOM ---
# Tested, working well
@router.post("/rooms/create")
async def create_room(data: RoomCreateRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    try:
        payload = verify_jwt(data.jwt)
        user_id = payload.get("user_id")
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid JWT")
    room = meeting_service.create_meeting(user_id, MeetingCreate(
        title=data.title,
        description=data.description,
        is_public=data.is_public,
        host_language=data.host_language
    ))
    return {"room_id": room.id, "title": room.title, "description": room.description, "is_public": room.is_public, "host_language": room.host_language}

############################################################################################################################################

# --- DELETE ROOM ---
# Tested, working well
@router.post("/rooms/delete")
async def delete_room(data: RoomDeleteRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    try:
        payload = verify_jwt(data.jwt)
        user_id = payload.get("user_id")
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid JWT")
    room = meeting_service.get_meeting_by_id(data.room_id)
    if not room or room.host_id != user_id:
        raise HTTPException(status_code=403, detail="Not allowed")
    meeting_service.delete_meeting(room.id)
    return {"status": "ok"}

############################################################################################################################################

# --- SET LANGUAGE LIMIT ---
# Tested but the limit is not shown anywhere, nor works
@router.post("/rooms/set_lang_limit")
async def set_lang_limit(data: RoomSetLangLimitRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    try:
        payload = verify_jwt(data.jwt)
        user_id = payload.get("user_id")
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid JWT")
    room = meeting_service.get_meeting_by_id(data.room_id)
    if not room or room.host_id != user_id:
        raise HTTPException(status_code=403, detail="Not allowed")
    meeting_service.set_room_lang_limit(room.id, data.lang_limit)
    return {"status": "ok"}

############################################################################################################################################

# --- ROOM CHAT ---
# Tested, working well
@router.get("/rooms/chat")
async def get_room_chat(request: Request, meeting_service: MeetingService = Depends(get_meeting_service)):
    # Accept JSON body for GET
    try:
        body = await request.json()
    except Exception:
        body = {}
    room_id = body.get("room_id")
    jwt = body.get("jwt")
    viewer_id = body.get("viewer_id")
    user_id = None
    if jwt:
        try:
            payload = verify_jwt(jwt)
            user_id = payload.get("user_id")
        except Exception:
            pass
    if not room_id:
        raise HTTPException(status_code=400, detail="room_id required")
    messages = meeting_service.get_room_chat(room_id, user_id, viewer_id)
    return {"messages": messages}

@router.post("/rooms/chat")
async def post_room_chat(data: RoomChatRequest, meeting_service: MeetingService = Depends(get_meeting_service)):
    user_id = None
    if data.jwt:
        try:
            payload = verify_jwt(data.jwt)
            user_id = payload.get("user_id")
        except Exception:
            pass
    meeting_service.add_room_chat_message(data.room_id, user_id, data.viewer_id, data.viewer_name, data.text)
    return {"status": "ok"}

############################################################################################################################################

# --- REQUEST A TARGET LANGUAGE ---
# New endpoint to request Cloudflare stream key and start streaming for a language
@router.post("/rooms/{room_id}/request_language")
async def request_language(room_id: int, body: RequestLanguageBody):
    """
    Requests a Cloudflare stream for the given language, registers the public playback URL, and returns status.
    """
    buffer = await buffer_manager.get_buffer(str(room_id))
    if buffer is None:
        raise HTTPException(status_code=404, detail="Room buffer not found")
    # Check if language already exists in stream manager
    if body.lang_code in buffer.stream_manager.streams:
        return {"status": "already_exists", "lang_code": body.lang_code}
    # Request Cloudflare stream key
    rtmps_url, stream_key, input_id = buffer.stream_manager.request_cloudflare_stream_key(
        body.lang_code, body.stream_name)
    # Start streaming for this language
    buffer.stream_manager.start_stream(body.lang_code, rtmps_url, stream_key)
    # Register public playback URL for this language in room metadata
    from os import getenv
    account_id = getenv("CLOUDFLARE_ACCOUNT_ID")
    playback_url = f"https://customer-{account_id}.cloudflarestream.com/{input_id}"
    # Save playback_url in room's language metadata (assume MeetingLanguage model and service exist)
    from src.api.models import MeetingLanguage
    from src.meetings.service import MeetingService
    meeting_service = MeetingService(buffer)
    # Try to find or create MeetingLanguage for this room/language
    existing_langs = meeting_service.get_meeting_languages(int(room_id))
    lang_obj = next((l for l in existing_langs if l.language_code == body.lang_code), None)
    if lang_obj:
        lang_obj.stream_url = playback_url
        meeting_service.db.commit()
    else:
        meeting_service.add_language_to_meeting(int(room_id), MeetingLanguage(
            meeting_id=int(room_id),
            language_code=body.lang_code,
            language_name=body.lang_code,
            stream_url=playback_url
        ))
    return {"status": "started", "lang_code": body.lang_code, "playback_url": playback_url}
