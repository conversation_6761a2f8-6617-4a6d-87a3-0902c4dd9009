"""
Database Models for the Omnispeak Backend

SQLModel-based database models for meetings, guests, and translation sessions.
Note: User authentication models are now in src/auth/models.py
"""
from typing import Optional, List
from datetime import datetime
from sqlmodel import Field, SQLModel, Relationship
from pydantic import BaseModel

# Maximum number of languages per meeting
MAX_LANGUAGES_PER_MEETING = 5


class Meeting(SQLModel, table=True):
    """Meeting model for translation sessions"""
    id: Optional[int] = Field(default=None, primary_key=True)
    host_id: int  # References User from auth - will be set up properly when all models are imported
    title: str
    description: Optional[str] = None
    host_language: str  # Original language of the host
    max_languages: int = Field(default=MAX_LANGUAGES_PER_MEETING)
    status: str = "created"  # created, live, ended, cancelled
    meeting_code: str = Field(unique=True, index=True)  # Short code for joining
    is_public: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    lang_limit: Optional[int] = None

    # Relationships
    languages: List["MeetingLanguage"] = Relationship(back_populates="meeting")
    participants: List["MeetingParticipant"] = Relationship(back_populates="meeting")


class MeetingLanguage(SQLModel, table=True):
    """Language streams available in a meeting"""
    id: Optional[int] = Field(default=None, primary_key=True)
    meeting_id: int = Field(foreign_key="meeting.id")
    language_code: str  # ISO language code (e.g., 'en', 'es', 'fr')
    language_name: str  # Human readable name (e.g., 'English', 'Spanish')
    stream_url: Optional[str] = None  # Cloudflare Stream URL
    cloudflare_video_uid: Optional[str] = None
    status: str = "pending"  # pending, ready, streaming, error
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    meeting: Meeting = Relationship(back_populates="languages")


class MeetingParticipant(SQLModel, table=True):
    """Participants in a meeting (both hosts and guests)"""
    id: Optional[int] = Field(default=None, primary_key=True)
    meeting_id: int = Field(foreign_key="meeting.id")
    user_id: Optional[int] = None  # References User from auth
    session_id: Optional[int] = None  # References Session from auth
    participant_type: str  # "host", "guest", or "viewer"
    display_name: str
    selected_language: Optional[str] = None  # Language they're listening to
    viewer_id: Optional[str] = None  # Unique viewer ID for client-side tracking
    joined_at: datetime = Field(default_factory=datetime.utcnow)
    left_at: Optional[datetime] = None
    is_active: bool = True

    # Relationships
    meeting: Meeting = Relationship(back_populates="participants")


class TranslationSession(SQLModel, table=True):
    """Active translation session for a specific language in a meeting"""
    id: Optional[int] = Field(default=None, primary_key=True)
    session_id: str = Field(unique=True)  # WebSocket session ID
    meeting_id: int = Field(foreign_key="meeting.id")
    language_id: int = Field(foreign_key="meetinglanguage.id")
    target_language: str
    original_audio_path: Optional[str] = None
    original_video_path: Optional[str] = None
    translated_audio_path: Optional[str] = None
    final_video_path: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "active"  # active, completed, error
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None


# Pydantic models for API requests/responses
class MeetingCreate(BaseModel):
    """Meeting creation request"""
    title: str
    description: Optional[str] = None
    host_language: str = "en"
    is_public: bool = True


class MeetingUpdate(BaseModel):
    """Meeting update request"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None


class MeetingLanguageCreate(BaseModel):
    """Add language to meeting request"""
    language_code: str
    language_name: str


class MeetingJoin(BaseModel):
    """Join meeting request"""
    meeting_code: str
    display_name: str
    selected_language: Optional[str] = None


class MeetingResponse(BaseModel):
    """Meeting response"""
    id: int
    title: str
    description: Optional[str]
    host_language: str
    status: str
    meeting_code: str
    is_public: bool
    created_at: datetime
    languages: List[dict]
    participant_count: int


class MeetingLanguageResponse(BaseModel):
    """Meeting language response"""
    id: int
    language_code: str
    language_name: str
    status: str
    stream_url: Optional[str]
    participant_count: int

class ChatMessage(SQLModel, table=True):
    """Chat messages for a meeting room."""
    id: Optional[int] = Field(default=None, primary_key=True)
    room_id: int = Field(foreign_key="meeting.id")
    user_id: Optional[int] = None
    viewer_id: Optional[str] = None
    sender_name: Optional[str] = None
    text: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)