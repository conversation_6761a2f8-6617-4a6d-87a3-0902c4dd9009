"""
Omnispeak Backend - Main FastAPI Application

A clean, well-structured FastAPI application for live video translation services.
"""
import asyncio
import logging
import uuid
import wave
import time
import re
from pathlib import Path
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlmodel import create_engine

# Import from new structure
from .models import Meeting, MeetingLanguage, MeetingParticipant, TranslationSession
from .websocket import (
    WebSocketCallbacks, handle_websocket_messages, active_connections,
    MAX_CONCURRENT_CONNECTIONS
)
from .auth_routes import router as auth_router
from .meeting_routes import router as meeting_router
from .database import create_db_and_tables, get_db_session
from src.auth.models import User, Session
from src.core.pipeline import pipeline_manager, PipelineConfig
from src.services.video.processor import VideoProcessor
from src.services.cloudflare.stream import CloudflareStreamClient
from src.utils.buffer import buffer_manager
from src.utils.chat import ChatDatabase, ChatMessageInc
from src.utils.config import (
    INPUT_SAMPLE_RATE, ORIGINAL_AUDIO_DIR, CLOUDFLARE_API_TOKEN,
    CLOUDFLARE_ACCOUNT_ID, LOG_LEVEL, LOG_FORMAT, ensure_directories
)

# Logging setup
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)
logger = logging.getLogger(__name__)

# Initialization
ensure_directories()

# Global chat database instance
chat_db = ChatDatabase()



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    create_db_and_tables()
    logger.info("Database tables created")

    # Initial user creation if needed
    from src.meetings.service import MeetingService
    from src.auth.models import User
    import os
    # You can set OMNISPEAK_INIT_USER and OMNISPEAK_INIT_PASS as env vars for first user
    init_user = "cat"
    init_pass = "KeyboardIsMyFavoriteBed"
    if init_user and init_pass:
        # Use DB session context
        from src.api.database import get_db_session_context
        with get_db_session_context() as db:
            service = MeetingService(db)
            created = service.create_initial_user_if_none(init_user, init_pass)
            if created:
                logger.info(f"Initial user '{init_user}' created.")
            else:
                logger.info("Initial user already exists, skipping creation.")

    yield

    # Cleanup pipelines on shutdown
    await pipeline_manager.stop_all_pipelines()
    logger.info("All pipelines stopped")


# Create FastAPI app
app = FastAPI(
    title="Omnispeak Backend",
    description="Live video translation service with real-time speech processing",
    version="0.2.0",
    lifespan=lifespan,
    docs_url=None,
    redoc_url=None
)

# Enable CORS for all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router)
app.include_router(meeting_router)


@app.get("/docs", include_in_schema=False)
def custom_scalar_docs(request: Request):
    """Custom API documentation using Scalar"""
    from scalar_fastapi import get_scalar_api_reference
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.post("/chat")
async def post_chat_message(msg: ChatMessageInc, request: Request):
    """Post a chat message"""
    from src.utils.chat import get_listener_id_from_jwt, ChatMessage
    
    listener_id = get_listener_id_from_jwt(request)
    chat_message = ChatMessage(
        text=msg.text,
        sender_id=listener_id,
        timestamp=datetime.now(),
        room_id=msg.room_id
    )
    chat_db.save_message(chat_message)
    return JSONResponse({"status": "ok"})


@app.get("/api/pipeline/stats")
async def get_pipeline_stats():
    """Get statistics for all active pipelines"""
    return JSONResponse(pipeline_manager.get_total_stats())


@app.get("/api/pipeline/sessions")
async def get_active_sessions():
    """Get list of active translation sessions"""
    return JSONResponse({"active_sessions": pipeline_manager.get_active_sessions()})


async def finalize_video_processing(session_id, translated_audio_buffer, video_processor, cloudflare_client, target_lang):
    """Finalize video processing with the accumulated translated audio"""
    try:
        await video_processor.stop_recording()
        if not video_processor.original_video_path.exists():
            logger.warning("No video file found, skipping video processing")
            return
            
        if not translated_audio_buffer:
            logger.warning("No translated audio found, skipping video processing")
            return
            
        # Save accumulated translated audio to file
        timestamp = int(time.time())
        safe_session_id = re.sub(r'[^\w\.\-]', '_', session_id)
        from src.utils.config import TRANSLATED_AUDIO_DIR, OUTPUT_SAMPLE_RATE
        translated_audio_path = Path(TRANSLATED_AUDIO_DIR) / f"translation_{target_lang}_{timestamp}_{safe_session_id}.wav"
        
        with wave.open(str(translated_audio_path), "wb") as wf:
            wf.setnchannels(1)
            wf.setsampwidth(2)
            wf.setframerate(OUTPUT_SAMPLE_RATE)
            wf.writeframes(bytes(translated_audio_buffer))
            
        logger.info(f"Saved accumulated translated audio: {translated_audio_path} ({len(translated_audio_buffer)} bytes)")
        
        # Merge with video
        final_video_path = await video_processor.merge_with_audio(translated_audio_path)
        logger.info(f"Video merged successfully: {final_video_path}")
        
        # Upload to Cloudflare if configured
        if cloudflare_client:
            try:
                video_name = f"translated_{target_lang}_{session_id}"
                upload_result = await cloudflare_client.upload_video(final_video_path, video_name)
                logger.info(f"Video uploaded to Cloudflare Stream: {upload_result.get('uid')}")
            except Exception as e:
                logger.error(f"Error uploading to Cloudflare Stream: {e}")
                
    except Exception as e:
        logger.error(f"Error in finalize_video_processing: {e}")


async def cleanup_session(session_id, video_processor, buffer):
    """Clean up session resources"""
    try:
        await video_processor.stop_recording()
        video_processor.cleanup()
        await buffer_manager.remove_buffer(session_id)
        
        # Stop pipeline
        await pipeline_manager.stop_pipeline(session_id)
        
        logger.info(f"Session {session_id} cleaned up successfully")
    except Exception as e:
        logger.error(f"Error cleaning up session {session_id}: {e}")


@app.websocket("/ws/{src_lang}/{target_lang}")
async def websocket_endpoint(websocket: WebSocket, src_lang: str, target_lang: str):
    """Main WebSocket endpoint for live translation"""
    await websocket.accept()
    session_id = str(uuid.uuid4())
    logger.info(f"New WebSocket connection. Translating from {src_lang} to {target_lang}. Session: {session_id}")
    logger.info(f"Client address: {websocket.client}")
    
    # Check if we have too many concurrent connections
    if len(active_connections) >= MAX_CONCURRENT_CONNECTIONS:
        logger.warning(f"Rate limit: Too many concurrent connections ({len(active_connections)}/{MAX_CONCURRENT_CONNECTIONS}). Rejecting connection {session_id}")
        await websocket.close(code=1008, reason="Too many concurrent connections. Please try again later.")
        return
    
    # Add to active connections
    active_connections.add(session_id)
    logger.info(f"Added session {session_id} to active connections. Total: {len(active_connections)}")
    
    # Track session start time for debugging empty sessions
    session_start_time = time.time()
    
    # Create enhanced pipeline configuration with better context
    pipeline_config = PipelineConfig(
        target_language=target_lang,
        source_language=src_lang,  # Use src_lang from path
        max_context_sentences=5,  # Increased from 3 for better context
        sentence_timeout=1.8,  # Slightly longer for better sentence completion
        use_gladia_stt=True,  # Use Gladia for real-time STT
        use_openai_whisper=False,  # Disable OpenAI Whisper
        use_google_stt=False,  # Disable Google STT
        use_google_tts=True,
        max_concurrent_tts=2,
        voice_gender="NEUTRAL",
        speaking_rate=1.0
    )
    
    # Initialize components
    video_processor = VideoProcessor(session_id)
    buffer = await buffer_manager.create_buffer(session_id)
    cloudflare_client = None
    original_audio_path = Path(ORIGINAL_AUDIO_DIR) / f"{session_id}_original.wav"
    original_wf = wave.open(str(original_audio_path), "wb")
    original_wf.setnchannels(1)
    original_wf.setsampwidth(2)
    original_wf.setframerate(INPUT_SAMPLE_RATE)
    
    # Create and configure pipeline
    pipeline = await pipeline_manager.create_pipeline(session_id, pipeline_config)
    
    # Set up WebSocket callbacks
    ws_callbacks = WebSocketCallbacks(websocket, session_id)
    pipeline.set_transcription_callback(ws_callbacks.on_transcription)
    pipeline.set_translation_callback(ws_callbacks.on_translation)
    pipeline.set_audio_output_callback(ws_callbacks.on_audio_output)

    try:
        if CLOUDFLARE_API_TOKEN and CLOUDFLARE_ACCOUNT_ID:
            cloudflare_client = CloudflareStreamClient()

        # Start the pipeline
        await pipeline.start()
        logger.info("Translation pipeline started successfully")
        
        # Handle WebSocket messages
        await handle_websocket_messages(websocket, session_id, pipeline, video_processor, buffer, original_wf)

    except WebSocketDisconnect:
        logger.info(f"Client disconnected gracefully. Session: {session_id}")
    except Exception as e:
        logger.error(f"Error during WebSocket session {session_id}: {e}")
    finally:
        # Cleanup logic here...
        # (This would be the same as in the original main.py)
        
        # Remove from active connections
        active_connections.discard(session_id)
        logger.info(f"Removed session {session_id} from active connections. Remaining: {len(active_connections)}")
        
        # Log session statistics
        session_duration = time.time() - session_start_time
        logger.info(f"Connection with {websocket.client} closed. Session: {session_id}")
        logger.info(f"Session duration: {session_duration:.1f}s")
