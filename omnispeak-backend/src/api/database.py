"""
Database Connection and Session Management

Handles SQLModel database connections and session management.
"""
from sqlmodel import create_engine, Session, SQLModel
from contextlib import contextmanager
from typing import Generator

# Database configuration
DATABASE_URL = "sqlite:///./omnispeak.db"
engine = create_engine(DATABASE_URL, echo=False)


def create_db_and_tables():
    """Create database and all tables"""
    SQLModel.metadata.create_all(engine)


def get_db_session() -> Generator[Session, None, None]:
    """Get database session for dependency injection"""
    with Session(engine) as session:
        yield session


@contextmanager
def get_db_session_context():
    """Get database session for context manager usage"""
    with Session(engine) as session:
        yield session
