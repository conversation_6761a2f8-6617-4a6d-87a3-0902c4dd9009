"""
Authentication Dependencies

FastAPI dependencies for authentication and authorization.
"""
from typing import Optional, <PERSON><PERSON>
from fastapi import Depends, HTTPException, status, Request, <PERSON><PERSON>
from sqlmodel import Session as DBSession

from .service import AuthService
from .models import User, Session
from src.api.database import get_db_session


def get_auth_service(db: DBSession = Depends(get_db_session)) -> AuthService:
    """Get authentication service instance"""
    return AuthService(db)


def get_session_token(request: Request, session_token: Optional[str] = <PERSON><PERSON>(None)) -> Optional[str]:
    """Extract session token from cookie"""
    return session_token


def get_current_user_optional(
    session_token: Optional[str] = Depends(get_session_token),
    auth_service: AuthService = Depends(get_auth_service)
) -> Optional[Tuple[Optional[User], Session]]:
    """Get current user (optional - returns None if not authenticated)"""
    if not session_token:
        return None
    
    return auth_service.get_current_user(session_token)


def get_current_user_required(
    current_user: Optional[Tuple[Optional[User], Session]] = Depends(get_current_user_optional)
) -> <PERSON>ple[Optional[User], Session]:
    """Get current user (required - raises exception if not authenticated)"""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    return current_user


def get_current_host(
    current_user: Tuple[Optional[User], Session] = Depends(get_current_user_required)
) -> User:
    """Get current host user (requires host authentication)"""
    user, session = current_user
    
    if session.user_type != "host" or not user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Host authentication required"
        )
    
    return user


def get_current_guest(
    current_user: Tuple[Optional[User], Session] = Depends(get_current_user_required)
) -> Session:
    """Get current guest session (requires guest authentication)"""
    user, session = current_user
    
    if session.user_type != "guest":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Guest authentication required"
        )
    
    return session


def get_current_user_any(
    current_user: Tuple[Optional[User], Session] = Depends(get_current_user_required)
) -> Tuple[Optional[User], Session]:
    """Get current user (host or guest)"""
    return current_user
