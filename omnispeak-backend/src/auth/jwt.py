"""
JWT utility functions for authentication
"""
import jwt
from datetime import datetime, timedelta
from typing import Any, Dict

# Secret key for JWT (use env var in production)
SECRET_KEY = "supersecretkeysupersecretkey1111"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24


def create_jwt_for_user(user: Any) -> str:
    """Create JWT for a user object (expects user.id and user.username)"""
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    payload = {
        "user_id": user.id,
        "username": getattr(user, "email", ""),
        "exp": expire
    }
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALG<PERSON>ITHM)
    return token


def verify_jwt(token: str) -> Dict:
    """Verify JWT and return payload, raises jwt exceptions if invalid"""
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALG<PERSON>ITHM])
    return payload
