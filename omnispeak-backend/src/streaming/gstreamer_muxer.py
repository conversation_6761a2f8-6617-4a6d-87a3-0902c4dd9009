import gi
import threading

gi.require_version('Gst', '1.0')
from gi.repository import Gst, GObject

Gst.init(None)

class GStreamerMuxer:
    """
    Real-time muxer for H.264 video and AAC audio, streaming to RTMPS endpoint.
    """
    def __init__(self, rtmps_url, stream_key):
        self.rtmps_url = rtmps_url
        self.stream_key = stream_key
        self.pipeline = None
        self.video_appsrc = None
        self.audio_appsrc = None
        self.lock = threading.Lock()
        self._init_pipeline()

    def _init_pipeline(self):
        # Build GStreamer pipeline for H.264 + AAC to RTMPS
        pipeline_str = (
            f'appsrc name=video_src is-live=true format=time ! h264parse ! flvmux name=mux streamable=true '
            f'appsrc name=audio_src is-live=true format=time ! audioconvert ! voaacenc ! mux. '
            f'mux. ! rtmpsink location={self.rtmps_url}/{self.stream_key}'
        )
        self.pipeline = Gst.parse_launch(pipeline_str)
        self.video_appsrc = self.pipeline.get_by_name('video_src')
        self.audio_appsrc = self.pipeline.get_by_name('audio_src')
        self.pipeline.set_state(Gst.State.PLAYING)

    def push_video(self, frame_bytes):
        with self.lock:
            buf = Gst.Buffer.new_allocate(None, len(frame_bytes), None)
            buf.fill(0, frame_bytes)
            self.video_appsrc.emit('push-buffer', buf)

    def push_audio(self, audio_bytes):
        with self.lock:
            buf = Gst.Buffer.new_allocate(None, len(audio_bytes), None)
            buf.fill(0, audio_bytes)
            self.audio_appsrc.emit('push-buffer', buf)

    def stop(self):
        self.pipeline.set_state(Gst.State.NULL)
