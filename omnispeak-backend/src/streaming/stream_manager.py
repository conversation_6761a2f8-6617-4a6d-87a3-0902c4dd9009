import requests
import os
from src.streaming.gstreamer_muxer import GStreamerMuxer

class StreamManager:
    def request_cloudflare_stream_key(self, lang_code, stream_name=None):
        """
        Requests a new Cloudflare stream key for the given language.
        Returns (rtmps_url, stream_key, input_id).
        """
        api_token = os.getenv("CLOUDFLARE_API_TOKEN")
        account_id = os.getenv("CLOUDFLARE_ACCOUNT_ID")
        if not api_token or not account_id:
            raise Exception("Cloudflare API token or account ID not set in environment variables.")
        url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/stream/live_inputs"
        headers = {"Authorization": f"Bearer {api_token}", "Content-Type": "application/json"}
        data = {
            "meta": {"name": stream_name or f"stream_{lang_code}"},
            "recording": {"mode": "automatic"}
        }
        resp = requests.post(url, headers=headers, json=data)
        resp.raise_for_status()
        result = resp.json()["result"]
        rtmps_url = result["rtmps"]["url"]
        stream_key = result["rtmps"]["streamKey"]
        input_id = result["uid"]
        return rtmps_url, stream_key, input_id
    """
    Manages multiple GStreamerMuxer instances for each language stream.
    """
    def __init__(self):
        self.streams = {}  # {lang_code: GStreamerMuxer}

    def start_stream(self, lang_code, rtmps_url, stream_key):
        muxer = GStreamerMuxer(rtmps_url, stream_key)
        self.streams[lang_code] = muxer

    def push_video_to_all(self, frame_bytes):
        for muxer in self.streams.values():
            muxer.push_video(frame_bytes)

    def push_audio(self, lang_code, audio_bytes):
        if lang_code in self.streams:
            self.streams[lang_code].push_audio(audio_bytes)

    def stop_all(self):
        for muxer in self.streams.values():
            muxer.stop()
        self.streams.clear()
