<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Audio Debug Client</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .debug {
        background: #f0f0f0;
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
      }
      .status {
        padding: 5px;
        margin: 5px 0;
        border-radius: 3px;
      }
      .success {
        background: #d4edda;
        color: #155724;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
      }
      .warning {
        background: #fff3cd;
        color: #856404;
      }
      .info {
        background: #d1ecf1;
        color: #0c5460;
      }
      button {
        padding: 10px 20px;
        margin: 5px;
        font-size: 16px;
      }
      #debug-log {
        max-height: 400px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <h1>Audio Debug Client</h1>

    <div class="debug">
      <h3>Controls</h3>
      <label for="src-lang-select">Source Language:</label>
      <select id="src-lang-select">
        <option value="en-US">English (US)</option>
        <option value="uk-UA">Ukrainian</option>
        <option value="ru-RU">Russian</option>
        <option value="pl-PL">Polish</option>
        <option value="de-DE">German</option>
        <option value="fr-FR">French</option>
        <option value="es-ES">Spanish</option>
        <option value="zh-CN">Chinese</option>
        <option value="auto">Auto</option>
      </select>
      <button id="start-btn" onclick="startAudio()">
        Start Audio Streaming
      </button>
      <button id="stop-btn" onclick="stopAudio()" disabled>
        Stop Audio Streaming
      </button>
      <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="debug">
      <h3>Status</h3>
      <div id="mic-status" class="status info">Microphone: Not accessed</div>
      <div id="ws-status" class="status info">WebSocket: Not connected</div>
      <div id="audio-status" class="status info">Audio Processing: Stopped</div>
    </div>

    <div class="debug">
      <h3>Audio Stats</h3>
      <div>Packets Sent: <span id="packets-sent">0</span></div>
      <div>Total Bytes: <span id="total-bytes">0</span></div>
      <div>Current Volume: <span id="current-volume">0</span></div>
      <div>Sample Rate: <span id="sample-rate">Unknown</span></div>
      <div>Is Resampling: <span id="is-resampling">Unknown</span></div>
    </div>

    <div class="debug">
      <h3>Debug Log</h3>
      <div id="debug-log"></div>
    </div>

    <script>
      let audioContext = null;
      let audioSource = null;
      let audioProcessor = null;
      let audioStream = null;
      let audioSocket = null;
      let isStreaming = false;
      let packetsSent = 0;
      let totalBytes = 0;

      function log(message, level = "info") {
        const logDiv = document.getElementById("debug-log");
        const timestamp = new Date().toLocaleTimeString();
        const entry = document.createElement("div");
        entry.className = level;
        entry.innerHTML = `[${timestamp}] ${message}`;
        logDiv.appendChild(entry);
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(`[${level.toUpperCase()}]`, message);
      }

      function updateStatus(elementId, message, className) {
        const element = document.getElementById(elementId);
        element.textContent = message;
        element.className = `status ${className}`;
      }

      function updateStats() {
        document.getElementById("packets-sent").textContent = packetsSent;
        document.getElementById("total-bytes").textContent = totalBytes;
      }

      function clearLog() {
        document.getElementById("debug-log").innerHTML = "";
      }

      async function startAudio() {
        if (isStreaming) {
          log("Already streaming", "warning");
          return;
        }

        try {
          log("Starting audio streaming...");

          // Request microphone access
          updateStatus(
            "mic-status",
            "Requesting microphone access...",
            "warning"
          );
          const constraints = {
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
            },
          };

          audioStream = await navigator.mediaDevices.getUserMedia(constraints);
          updateStatus("mic-status", "Microphone access granted", "success");
          log("Microphone access granted");

          // Create AudioContext
          const AudioContextClass =
            window.AudioContext || window.webkitAudioContext;
          audioContext = new AudioContextClass();

          document.getElementById("sample-rate").textContent =
            audioContext.sampleRate;
          document.getElementById("is-resampling").textContent =
            audioContext.sampleRate !== 16000 ? "Yes" : "No";

          log(`AudioContext created: ${audioContext.sampleRate}Hz`);

          // Resume if suspended
          if (audioContext.state === "suspended") {
            await audioContext.resume();
            log("AudioContext resumed");
          }

          // Create audio processing chain
          audioSource = audioContext.createMediaStreamSource(audioStream);
          audioProcessor = audioContext.createScriptProcessor(4096, 1, 1);

          audioSource.connect(audioProcessor);
          audioProcessor.connect(audioContext.destination);

          log("Audio processing chain connected");

          // Get selected source language
          const srcLang = document.getElementById("src-lang-select").value;
          // Default target language (can be made selectable later)
          const targetLang = "en";
          // Connect WebSocket with src_lang and target_lang
          updateStatus("ws-status", "Connecting to WebSocket...", "warning");
          audioSocket = new WebSocket(`ws://127.0.0.1:8001/ws/${srcLang}/${targetLang}`);
          audioSocket.binaryType = "arraybuffer";

          audioSocket.onopen = () => {
            updateStatus("ws-status", "WebSocket connected", "success");
            log("WebSocket connected");

            // Send join-room message
            const joinMessage = {
              type: "join-room",
              roomId: "debug-room",
              role: "preacher",
              audioConfig: {
                sampleRate: audioContext.sampleRate,
                channelCount: 1,
                encoding: "pcm16",
              },
            };
            audioSocket.send(JSON.stringify(joinMessage));
            log("Sent join-room message");
          };

          audioSocket.onclose = (event) => {
            updateStatus(
              "ws-status",
              `WebSocket closed (${event.code}: ${event.reason})`,
              "error"
            );
            log(`WebSocket closed: ${event.code} - ${event.reason}`, "error");
          };

          audioSocket.onerror = (error) => {
            updateStatus("ws-status", "WebSocket error", "error");
            log(`WebSocket error: ${error}`, "error");
          };

          audioSocket.onmessage = (event) => {
            if (typeof event.data === "string") {
              log(`Received JSON: ${event.data}`);
            } else {
              log(`Received audio: ${event.data.byteLength} bytes`);
            }
          };

          // Audio processing
          audioProcessor.onaudioprocess = function (e) {
            if (!isStreaming || audioSocket.readyState !== WebSocket.OPEN) {
              return;
            }

            const input = e.inputBuffer.getChannelData(0);

            if (!input || input.length === 0) {
              log("No audio input data", "warning");
              return;
            }

            // Calculate volume
            let maxAmplitude = 0;
            for (let i = 0; i < input.length; i++) {
              maxAmplitude = Math.max(maxAmplitude, Math.abs(input[i]));
            }

            document.getElementById("current-volume").textContent =
              maxAmplitude.toFixed(4);

            // Resample to 16kHz if needed
            let processedSamples = input;
            const currentSampleRate = audioContext.sampleRate;
            const targetSampleRate = 16000;

            if (currentSampleRate !== targetSampleRate) {
              const ratio = currentSampleRate / targetSampleRate;
              const outputLength = Math.floor(input.length / ratio);
              const resampled = new Float32Array(outputLength);

              for (let i = 0; i < outputLength; i++) {
                const sourceIndex = Math.floor(i * ratio);
                resampled[i] = input[sourceIndex];
              }
              processedSamples = resampled;
            }

            // Convert to 16-bit PCM
            const pcm = new Int16Array(processedSamples.length);
            for (let i = 0; i < processedSamples.length; i++) {
              let s = Math.max(-1, Math.min(1, processedSamples[i]));
              pcm[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
            }

            // Send to server
            try {
              audioSocket.send(pcm.buffer);
              packetsSent++;
              totalBytes += pcm.buffer.byteLength;
              updateStats();

              if (packetsSent % 50 === 0) {
                log(
                  `Sent ${packetsSent} packets (${totalBytes} bytes), volume: ${maxAmplitude.toFixed(
                    4
                  )}`
                );
              }
            } catch (error) {
              log(`Error sending audio: ${error}`, "error");
            }
          };

          isStreaming = true;
          updateStatus("audio-status", "Audio processing active", "success");
          document.getElementById("start-btn").disabled = true;
          document.getElementById("stop-btn").disabled = false;
          log("Audio streaming started successfully", "success");
        } catch (error) {
          log(`Error starting audio: ${error}`, "error");
          updateStatus("mic-status", `Error: ${error.message}`, "error");
          stopAudio();
        }
      }

      function stopAudio() {
        log("Stopping audio streaming...");
        isStreaming = false;

        if (audioProcessor) {
          audioProcessor.disconnect();
          audioProcessor.onaudioprocess = null;
          audioProcessor = null;
        }

        if (audioSource) {
          audioSource.disconnect();
          audioSource = null;
        }

        if (audioContext) {
          audioContext.close();
          audioContext = null;
        }

        if (audioStream) {
          audioStream.getTracks().forEach((track) => track.stop());
          audioStream = null;
        }

        if (audioSocket) {
          audioSocket.close();
          audioSocket = null;
        }

        updateStatus("mic-status", "Microphone: Not accessed", "info");
        updateStatus("ws-status", "WebSocket: Not connected", "info");
        updateStatus("audio-status", "Audio Processing: Stopped", "info");

        document.getElementById("start-btn").disabled = false;
        document.getElementById("stop-btn").disabled = true;

        log("Audio streaming stopped", "success");
      }

      // Add user interaction requirement for Safari
      document.addEventListener(
        "click",
        function () {
          if (audioContext && audioContext.state === "suspended") {
            audioContext.resume();
          }
        },
        { once: true }
      );
    </script>
  </body>
</html>
