"""
Omnispeak Backend - Entry Point

Clean entry point for the restructured live video translation service.
This file imports and runs the FastAPI application from the new structure.
"""
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import the FastAPI app from the new structure
from src.api.main import app

# This allows running with: uvicorn main:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
