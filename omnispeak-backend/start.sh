#!/bin/bash

if ! command -v python3 &> /dev/null; then
	echo "Python is not installed!"
	exit 1
fi

if ! command -v uv &> /dev/null; then
	echo "uv is not installed! Please install uv (pip install uv)."
	exit 1
fi

uv sync
if [ $? -ne 0 ]; then
	echo "uv sync failed. Please fix the dependencies manually."
	exit 1
fi

# Activate virtual environment only if not already active
if [ -z "$VIRTUAL_ENV" ]; then
	if [ -f ".venv/bin/activate" ]; then
		source .venv/bin/activate
	else
		echo "Virtual environment not found. Please create it with 'python3 -m venv .venv' and install dependencies."
		exit 1
	fi
fi

# Run server
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8001

