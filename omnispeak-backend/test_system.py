#!/usr/bin/env python3
"""
Test script for the live translation system
"""
import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_imports():
    """Test that all required modules can be imported"""
    try:
        logger.info("Testing imports...")
        
        # Test config
        import config
        logger.info("✓ Config module imported")
        
        # Test video processor
        from video_processor import VideoProcessor
        logger.info("✓ VideoProcessor imported")
        
        # Test cloudflare stream
        from cloudflare_stream import CloudflareStreamClient
        logger.info("✓ CloudflareStreamClient imported")
        
        # Test buffer manager
        from buffer_manager import buffer_manager
        logger.info("✓ BufferManager imported")
        
        # Test main mvp module
        import mvp
        logger.info("✓ MVP module imported")
        
        logger.info("All imports successful!")
        return True
        
    except Exception as e:
        logger.error(f"Import failed: {e}")
        return False

async def test_config():
    """Test configuration setup"""
    try:
        logger.info("Testing configuration...")
        
        import config
        
        # Test directory creation
        config.ensure_directories()
        
        # Check if directories exist
        directories = [
            config.TEMP_DIR,
            config.RECORDINGS_DIR,
            config.ORIGINAL_AUDIO_DIR,
            config.ORIGINAL_VIDEO_DIR,
            config.TRANSLATED_AUDIO_DIR,
            config.FINAL_VIDEO_DIR
        ]
        
        for directory in directories:
            if Path(directory).exists():
                logger.info(f"✓ Directory exists: {directory}")
            else:
                logger.error(f"✗ Directory missing: {directory}")
                return False
        
        logger.info("Configuration test successful!")
        return True
        
    except Exception as e:
        logger.error(f"Configuration test failed: {e}")
        return False

async def test_video_processor():
    """Test video processor initialization"""
    try:
        logger.info("Testing VideoProcessor...")
        
        from video_processor import VideoProcessor
        
        # Create a test session
        session_id = "test_session_123"
        processor = VideoProcessor(session_id)
        
        logger.info(f"✓ VideoProcessor created for session: {session_id}")
        logger.info(f"✓ Original video path: {processor.original_video_path}")
        logger.info(f"✓ Final video path: {processor.final_video_path}")
        
        # Test cleanup
        processor.cleanup()
        logger.info("✓ VideoProcessor cleanup successful")
        
        logger.info("VideoProcessor test successful!")
        return True
        
    except Exception as e:
        logger.error(f"VideoProcessor test failed: {e}")
        return False

async def test_buffer_manager():
    """Test buffer manager"""
    try:
        logger.info("Testing BufferManager...")
        
        from buffer_manager import buffer_manager
        
        # Create a test buffer
        session_id = "test_buffer_session"
        buffer = await buffer_manager.create_buffer(session_id)
        
        logger.info(f"✓ Buffer created for session: {session_id}")
        
        # Test adding chunks
        await buffer.add_audio_chunk(b"test_audio_data")
        await buffer.add_video_chunk(b"test_video_data")
        
        logger.info("✓ Audio and video chunks added")
        
        # Test buffer status
        status = await buffer.get_buffer_status()
        logger.info(f"✓ Buffer status: {status}")
        
        # Cleanup
        await buffer_manager.remove_buffer(session_id)
        logger.info("✓ Buffer removed")
        
        logger.info("BufferManager test successful!")
        return True
        
    except Exception as e:
        logger.error(f"BufferManager test failed: {e}")
        return False

async def test_cloudflare_client():
    """Test Cloudflare client (without actual API calls)"""
    try:
        logger.info("Testing CloudflareStreamClient...")
        
        from cloudflare_stream import CloudflareStreamClient
        import config
        
        # Test initialization without real credentials
        if config.CLOUDFLARE_API_TOKEN and config.CLOUDFLARE_ACCOUNT_ID:
            client = CloudflareStreamClient()
            logger.info("✓ CloudflareStreamClient initialized with real credentials")
        else:
            logger.info("⚠ No Cloudflare credentials configured (this is OK for testing)")
            # Test with dummy credentials to check initialization
            try:
                client = CloudflareStreamClient("dummy_token", "dummy_account")
                logger.info("✓ CloudflareStreamClient initialized with dummy credentials")
            except ValueError as e:
                if "must be set" in str(e):
                    logger.info("✓ CloudflareStreamClient properly validates credentials")
                else:
                    raise
        
        logger.info("CloudflareStreamClient test successful!")
        return True
        
    except Exception as e:
        logger.error(f"CloudflareStreamClient test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("Starting system tests...")
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("VideoProcessor", test_video_processor),
        ("BufferManager", test_buffer_manager),
        ("CloudflareClient", test_cloudflare_client),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)
