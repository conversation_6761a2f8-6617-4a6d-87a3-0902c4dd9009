<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preacher Meet - WebRTC Live Translation</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .container {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        max-width: 900px;
        width: 90%;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .header h1 {
        color: #333;
        margin-bottom: 10px;
      }

      .header p {
        color: #666;
        font-size: 14px;
      }

      .video-container {
        position: relative;
        background: #000;
        border-radius: 15px;
        overflow: hidden;
        margin-bottom: 20px;
        aspect-ratio: 16/9;
      }

      #localVideo {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transform: scaleX(-1);
      }

      .video-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #888;
        font-size: 18px;
      }

      .controls {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }

      .control-btn {
        padding: 12px 20px;
        border: none;
        border-radius: 50px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 140px;
        justify-content: center;
      }

      .control-btn.camera-on {
        background: #4caf50;
        color: white;
      }
      .control-btn.camera-off {
        background: #f44336;
        color: white;
      }
      .control-btn.mic-on {
        background: #2196f3;
        color: white;
      }
      .control-btn.mic-off {
        background: #ff9800;
        color: white;
      }
      .control-btn.streaming {
        background: #9c27b0;
        color: white;
      }
      .control-btn.stopped {
        background: #607d8b;
        color: white;
      }

      .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      .control-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }

      .config-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
      }

      .config-box {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
      }

      .config-box h3 {
        margin-bottom: 10px;
        color: #333;
        font-size: 16px;
      }

      .config-box label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        font-size: 12px;
        color: #666;
      }

      .config-box input,
      .config-box select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 13px;
        margin-bottom: 10px;
      }

      .status {
        background: #e8f5e8;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
      }

      .status-item {
        display: flex;
        justify-content: space-between;
        font-size: 13px;
        padding: 5px 0;
      }

      .status-value {
        font-weight: 600;
      }

      .status-value.good {
        color: #4caf50;
      }
      .status-value.warning {
        color: #ff9800;
      }
      .status-value.error {
        color: #f44336;
      }

      .stream-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .stream-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      .stream-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .hidden {
        display: none;
      }

      @media (max-width: 768px) {
        .config-section {
          grid-template-columns: 1fr;
        }
        .container {
          padding: 20px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎤 Preacher Meet</h1>
        <p>WebRTC Live Translation Platform</p>
      </div>

      <div class="video-container">
        <video
          id="localVideo"
          autoplay
          muted
          playsinline
          class="hidden"
        ></video>
        <div id="videoPlaceholder" class="video-placeholder">
          📹 Camera is off - Enable to start preview
        </div>
      </div>

      <div class="controls">
        <button id="cameraBtn" class="control-btn camera-off">
          📹 Camera Off
        </button>
        <button id="micBtn" class="control-btn mic-off">🎤 Mic Off</button>
        <button id="broadcastBtn" class="control-btn stopped">
          📡 Start Broadcast
        </button>
      </div>

      <div class="config-section">
        <div class="config-box">
          <h3>🔧 Connection Settings</h3>
          <label>Signaling Server:</label>
          <input
            type="url"
            id="signalingUrl"
            placeholder="wss://your-signaling-server.com"
            value="ws://127.0.0.1:8001"
            disabled
          />
          <label>Room ID:</label>
          <input
            type="text"
            id="roomId"
            placeholder="preacher-room-123"
            value="sermon-hall-1"
          />
          <label for="languageSelect">Translation Language:</label>
          <select id="languageSelect">
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
          </select>
        </div>

        <div class="config-box">
          <h3>🎛️ Stream Quality</h3>
          <label>Video Quality:</label>
          <select id="videoQuality">
            <option value="720p">720p (Recommended)</option>
            <option value="1080p">1080p (High Quality)</option>
            <option value="480p">480p (Low Bandwidth)</option>
          </select>

          <label>Audio Bitrate:</label>
          <select id="audioBitrate">
            <option value="128">128 kbps (Standard)</option>
            <option value="192">192 kbps (High Quality)</option>
            <option value="64">64 kbps (Low Bandwidth)</option>
          </select>
        </div>
      </div>

      <div class="status">
        <div class="status-item">
          <span>Camera:</span>
          <span id="cameraStatus" class="status-value">Off</span>
        </div>
        <div class="status-item">
          <span>Microphone:</span>
          <span id="micStatus" class="status-value">Off</span>
        </div>
        <div class="status-item">
          <span>Signaling:</span>
          <span id="signalingStatus" class="status-value">Disconnected</span>
        </div>
        <div class="status-item">
          <span>Broadcasting:</span>
          <span id="broadcastStatus" class="status-value">Stopped</span>
        </div>
        <div class="status-item">
          <span>Connected Viewers:</span>
          <span id="viewerCount" class="status-value">0</span>
        </div>
        <div class="status-item">
          <span>Network Quality:</span>
          <span id="networkQuality" class="status-value">Unknown</span>
        </div>
      </div>

      <button id="startBroadcastBtn" class="stream-btn">
        🚀 Start Broadcasting Sermon
      </button>

      <div class="config-box" style="margin-top: 20px">
        <h3>💬 Listener Chat</h3>
        <label for="jwtInput">JWT Token:</label>
        <input
          id="jwtInput"
          type="text"
          placeholder="Paste your JWT token here"
          style="margin-bottom: 10px"
        />
        <label for="chatInput">Message:</label>
        <input
          id="chatInput"
          type="text"
          placeholder="Type your message..."
          style="margin-bottom: 10px"
        />
        <button id="sendChatBtn" class="stream-btn" style="margin-top: 10px">
          Send Message
        </button>
        <div
          id="chatStatus"
          style="margin-top: 10px; color: #4caf50; font-weight: 600"
        ></div>
      </div>

      <div class="config-box" style="margin-top: 20px">
        <h3>🔧 Audio Debugging</h3>
        <button
          id="testAudioBtn"
          class="stream-btn"
          style="margin-bottom: 10px"
        >
          🎵 Test Audio System
        </button>
        <button
          id="checkPermissionsBtn"
          class="stream-btn"
          style="margin-bottom: 10px"
        >
          🔒 Check Permissions
        </button>
        <div
          id="audioDebugStatus"
          style="
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
          "
        ></div>
      </div>

      <div class="config-box">
        <h3>�� Microphone Test</h3>
        <p>
          Test your microphone to ensure proper audio levels for speech
          recognition:
        </p>
        <button
          id="testMicBtn"
          class="btn btn-secondary"
          onclick="testMicrophone()"
        >
          Test Microphone
        </button>
        <div
          id="micTestResult"
          style="
            margin-top: 10px;
            min-height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
          "
        ></div>
      </div>
    </div>

    <script>
      class PreacherWebRTC {
        constructor() {
          this.localStream = null;
          this.peerConnections = new Map();
          this.signalingSocket = null;
          this.isPreacher = true;
          this.roomId = null;

          this.cameraEnabled = false;
          this.micEnabled = false;
          this.isBroadcasting = false;

          this.rtcConfig = {
            iceServers: [
              { urls: "stun:stun.l.google.com:19302" },
              { urls: "stun:stun1.l.google.com:19302" },
            ],
          };

          this.initializeElements();
          this.attachEventListeners();
        }

        initializeElements() {
          this.localVideo = document.getElementById("localVideo");
          this.videoPlaceholder = document.getElementById("videoPlaceholder");
          this.cameraBtn = document.getElementById("cameraBtn");
          this.micBtn = document.getElementById("micBtn");
          this.broadcastBtn = document.getElementById("broadcastBtn");
          this.startBroadcastBtn = document.getElementById("startBroadcastBtn");
          this.signalingUrl = document.getElementById("signalingUrl");
          this.roomIdInput = document.getElementById("roomId");
          this.videoQuality = document.getElementById("videoQuality");
          this.audioBitrate = document.getElementById("audioBitrate");
          this.languageSelect = document.getElementById("languageSelect");
          this.cameraStatus = document.getElementById("cameraStatus");
          this.micStatus = document.getElementById("micStatus");
          this.signalingStatus = document.getElementById("signalingStatus");
          this.broadcastStatus = document.getElementById("broadcastStatus");
          this.viewerCount = document.getElementById("viewerCount");
          this.networkQuality = document.getElementById("networkQuality");
        }

        attachEventListeners() {
          this.cameraBtn.addEventListener("click", () => this.toggleCamera());
          this.micBtn.addEventListener("click", () => this.toggleMic());
          this.broadcastBtn.addEventListener("click", () =>
            this.toggleBroadcast()
          );
          this.startBroadcastBtn.addEventListener("click", () =>
            this.startBroadcast()
          );
        }

        async toggleCamera() {
          try {
            if (!this.cameraEnabled) {
              // Logic to turn the camera ON
              const constraints = this.getMediaConstraints();
              const stream = await navigator.mediaDevices.getUserMedia({
                video: constraints.video, // Use the quality constraints
                audio: this.micEnabled, // Keep the current mic state
              });
              await this.updateLocalStream(stream);
              this.cameraEnabled = true;
              this.updateCameraUI(true);
            } else {
              // Logic to turn the camera OFF
              if (this.localStream) {
                this.localStream.getVideoTracks().forEach((track) => {
                  track.stop();
                  this.localStream.removeTrack(track);
                });
              }
              this.cameraEnabled = false;
              this.updateCameraUI(false);
              this.localVideo.classList.add("hidden");
              this.videoPlaceholder.classList.remove("hidden");
            }
          } catch (error) {
            console.error("Camera error:", error);
            this.showError("Camera access failed: " + error.message);
          }
        }
        async toggleMic() {
          try {
            if (!this.micEnabled) {
              // Logic to turn the mic ON
              const constraints = this.getMediaConstraints();
              const stream = await navigator.mediaDevices.getUserMedia({
                video: this.cameraEnabled, // Keep the current camera state
                audio: true, // Explicitly request audio
              });

              await this.updateLocalStream(stream);
              this.micEnabled = true;
              this.updateMicUI(true);
            } else {
              // Logic to turn the mic OFF
              if (this.localStream) {
                this.localStream.getAudioTracks().forEach((track) => {
                  track.stop();
                  this.localStream.removeTrack(track);
                });
              }
              this.micEnabled = false;
              this.updateMicUI(false);
            }
          } catch (error) {
            console.error("Microphone error:", error);
            this.showError("Microphone access failed: " + error.message);
          }
        }

        async updateLocalStream(newStream) {
          if (this.localStream) {
            this.localStream.getTracks().forEach((track) => track.stop());
          }
          this.localStream = newStream;
          this.localVideo.srcObject = newStream;
          if (this.cameraEnabled) {
            this.localVideo.classList.remove("hidden");
            this.videoPlaceholder.classList.add("hidden");
          }
          this.peerConnections.forEach(async (pc, viewerId) => {
            const senders = pc.getSenders();
            newStream.getTracks().forEach(async (track) => {
              const sender = senders.find(
                (s) => s.track && s.track.kind === track.kind
              );
              if (sender) {
                await sender.replaceTrack(track);
              } else {
                pc.addTrack(track, newStream);
              }
            });
          });
        }

        getMediaConstraints() {
          const quality = this.videoQuality.value;
          const audioKbps = parseInt(this.audioBitrate.value);
          const videoConstraints = {
            "480p": { width: 854, height: 480, frameRate: 30 },
            "720p": { width: 1280, height: 720, frameRate: 30 },
            "1080p": { width: 1920, height: 1080, frameRate: 30 },
          };
          return {
            video: this.cameraEnabled ? videoConstraints[quality] : false,
            audio: this.micEnabled
              ? {
                  echoCancellation: true,
                  noiseSuppression: true,
                  autoGainControl: true,
                  sampleRate: 48000,
                  channelCount: 2,
                }
              : false,
          };
        }

        async startBroadcast() {
          if (!this.cameraEnabled && !this.micEnabled) {
            this.showError("Please enable camera or microphone first");
            return;
          }
          this.roomId = this.roomIdInput.value.trim();
          if (!this.roomId) {
            this.showError("Please enter a room ID");
            return;
          }
          try {
            await this.connectToSignalingServer();
          } catch (error) {
            this.showError("Failed to start broadcast: " + error.message);
          }
        }

        async connectToSignalingServer() {
          const signalingUrlBase = this.signalingUrl.value.trim();
          const targetLanguage = this.languageSelect.value;
          const fullUrl = `${signalingUrlBase}/ws/${targetLanguage}`;

          if (!fullUrl) {
            throw new Error("Please enter a signaling server URL");
          }

          return new Promise((resolve, reject) => {
            this.signalingSocket = new WebSocket(fullUrl);
            this.signalingSocket.onopen = () => {
              console.log("Connected to signaling server");
              this.updateSignalingStatus("Connected", "good");
              this.sendSignalingMessage({
                type: "join-room",
                roomId: this.roomId,
                role: "preacher",
              });
              resolve();
            };
            this.signalingSocket.onmessage = (event) => {
              this.handleSignalingMessage(JSON.parse(event.data));
            };
            this.signalingSocket.onclose = () => {
              this.updateSignalingStatus("Disconnected", "error");
              this.isBroadcasting = false;
              this.updateBroadcastUI(false);
            };
            this.signalingSocket.onerror = (error) => {
              console.error("Signaling error:", error);
              this.updateSignalingStatus("Error", "error");
              reject(new Error("Signaling connection failed"));
            };
            setTimeout(() => {
              if (this.signalingSocket.readyState !== WebSocket.OPEN) {
                reject(new Error("Connection timeout"));
              }
            }, 5000);
          });
        }

        async handleSignalingMessage(message) {
          console.log("Received signaling message:", message);
          switch (message.type) {
            case "viewer-joined":
              await this.handleViewerJoined(message.viewerId);
              break;
            case "viewer-left":
              this.handleViewerLeft(message.viewerId);
              break;
            case "ice-candidate":
              await this.handleRemoteIceCandidate(message);
              break;
            case "answer":
              await this.handleAnswer(message);
              break;
            case "broadcast-started":
              this.isBroadcasting = true;
              this.updateBroadcastUI(true);
              break;
          }
        }

        async handleViewerJoined(viewerId) {
          console.log("Viewer joined:", viewerId);
          const peerConnection = new RTCPeerConnection(this.rtcConfig);
          this.peerConnections.set(viewerId, peerConnection);
          if (this.localStream) {
            this.localStream.getTracks().forEach((track) => {
              peerConnection.addTrack(track, this.localStream);
            });
          }
          peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
              this.sendSignalingMessage({
                type: "ice-candidate",
                viewerId: viewerId,
                candidate: event.candidate,
              });
            }
          };
          peerConnection.onconnectionstatechange = () => {
            console.log(
              `Connection state with ${viewerId}:`,
              peerConnection.connectionState
            );
            this.updateViewerCount();
          };
          const offer = await peerConnection.createOffer();
          await peerConnection.setLocalDescription(offer);
          this.sendSignalingMessage({
            type: "offer",
            viewerId: viewerId,
            offer: offer,
          });
          this.updateViewerCount();
        }

        async handleAnswer(message) {
          const peerConnection = this.peerConnections.get(message.viewerId);
          if (peerConnection) {
            await peerConnection.setRemoteDescription(message.answer);
          }
        }

        async handleRemoteIceCandidate(message) {
          const peerConnection = this.peerConnections.get(message.viewerId);
          if (peerConnection) {
            await peerConnection.addIceCandidate(message.candidate);
          }
        }

        handleViewerLeft(viewerId) {
          const peerConnection = this.peerConnections.get(viewerId);
          if (peerConnection) {
            peerConnection.close();
            this.peerConnections.delete(viewerId);
            this.updateViewerCount();
          }
        }

        sendSignalingMessage(message) {
          if (this.signalingSocket?.readyState === WebSocket.OPEN) {
            this.signalingSocket.send(JSON.stringify(message));
          }
        }

        toggleBroadcast() {
          if (this.isBroadcasting) {
            this.stopBroadcast();
          } else {
            this.startBroadcast();
          }
        }

        stopBroadcast() {
          this.peerConnections.forEach((pc) => pc.close());
          this.peerConnections.clear();
          if (this.signalingSocket) {
            this.signalingSocket.close();
          }

          // CRITICAL: Also stop audio streaming and close audio WebSocket
          if (typeof stopAudioStreaming === "function") {
            stopAudioStreaming();
          }

          this.isBroadcasting = false;
          this.updateBroadcastUI(false);
          this.updateSignalingStatus("Disconnected");
          this.updateViewerCount();
        }

        updateCameraUI(enabled) {
          this.cameraBtn.textContent = enabled
            ? "📹 Camera On"
            : "📹 Camera Off";
          this.cameraBtn.className = `control-btn ${
            enabled ? "camera-on" : "camera-off"
          }`;
          this.cameraStatus.textContent = enabled ? "On" : "Off";
          this.cameraStatus.className = `status-value ${enabled ? "good" : ""}`;
        }

        updateMicUI(enabled) {
          this.micBtn.textContent = enabled ? "🎤 Mic On" : "🎤 Mic Off";
          this.micBtn.className = `control-btn ${
            enabled ? "mic-on" : "mic-off"
          }`;
          this.micStatus.textContent = enabled ? "On" : "Off";
          this.micStatus.className = `status-value ${enabled ? "good" : ""}`;
        }

        updateBroadcastUI(broadcasting) {
          this.broadcastBtn.textContent = broadcasting
            ? "📡 Stop Broadcast"
            : "📡 Start Broadcast";
          this.broadcastBtn.className = `control-btn ${
            broadcasting ? "streaming" : "stopped"
          }`;
          this.startBroadcastBtn.textContent = broadcasting
            ? "⏹️ Stop Broadcasting"
            : "🚀 Start Broadcasting Sermon";
          this.broadcastStatus.textContent = broadcasting ? "Live" : "Stopped";
          this.broadcastStatus.className = `status-value ${
            broadcasting ? "good" : ""
          }`;
        }

        updateSignalingStatus(status, type = "") {
          this.signalingStatus.textContent = status;
          this.signalingStatus.className = `status-value ${type}`;
        }

        updateViewerCount() {
          const connectedCount = Array.from(
            this.peerConnections.values()
          ).filter((pc) => pc.connectionState === "connected").length;
          this.viewerCount.textContent = connectedCount;
          this.viewerCount.className = `status-value ${
            connectedCount > 0 ? "good" : ""
          }`;
        }

        showError(message) {
          alert("Error: " + message);
          console.error(message);
        }
      }

      document.addEventListener("DOMContentLoaded", () => {
        new PreacherWebRTC();

        const sendChatBtn = document.getElementById("sendChatBtn");
        const chatInput = document.getElementById("chatInput");
        const jwtInput = document.getElementById("jwtInput");
        const chatStatus = document.getElementById("chatStatus");
        const roomIdInput = document.getElementById("roomId");

        sendChatBtn.addEventListener("click", async () => {
          const jwt = jwtInput.value.trim();
          const text = chatInput.value.trim();
          const roomId = roomIdInput.value.trim();
          if (!jwt) {
            chatStatus.textContent = "Please enter your JWT token.";
            chatStatus.style.color = "#f44336";
            return;
          }
          if (!text) {
            chatStatus.textContent = "Message cannot be empty.";
            chatStatus.style.color = "#f44336";
            return;
          }
          if (!roomId) {
            chatStatus.textContent = "Room ID is required.";
            chatStatus.style.color = "#f44336";
            return;
          }
          try {
            const response = await fetch("http://127.0.0.1:8001/chat", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${jwt}`,
              },
              body: JSON.stringify({ text, room_id: roomId }),
            });
            if (response.ok) {
              chatStatus.textContent = "Message sent!";
              chatStatus.style.color = "#4caf50";
              chatInput.value = "";
              setTimeout(() => {
                chatStatus.textContent = "";
              }, 3000);
            } else {
              const data = await response.json();
              chatStatus.textContent = data.detail || "Failed to send message.";
              chatStatus.style.color = "#f44336";
            }
          } catch (err) {
            chatStatus.textContent = "Network error. Is the server running?";
            chatStatus.style.color = "#f44336";
          }
        });

        // Add event listeners for debug buttons
        const testAudioBtn = document.getElementById("testAudioBtn");
        const checkPermissionsBtn = document.getElementById(
          "checkPermissionsBtn"
        );

        testAudioBtn.addEventListener("click", async () => {
          testAudioBtn.disabled = true;
          testAudioBtn.textContent = "🔄 Testing...";

          const success = await testAudioSystem();

          testAudioBtn.disabled = false;
          testAudioBtn.textContent = success
            ? "✅ Test Passed"
            : "❌ Test Failed";

          setTimeout(() => {
            testAudioBtn.textContent = "🎵 Test Audio System";
          }, 3000);
        });

        checkPermissionsBtn.addEventListener("click", async () => {
          checkPermissionsBtn.disabled = true;
          checkPermissionsBtn.textContent = "🔄 Checking...";

          await checkPermissions();

          checkPermissionsBtn.disabled = false;
          checkPermissionsBtn.textContent = "✅ Check Complete";

          setTimeout(() => {
            checkPermissionsBtn.textContent = "🔒 Check Permissions";
          }, 3000);
        });
      });

      // --- Audio Streaming to Backend WebSocket for Translation ---
      let audioSocket = null;
      let audioStream = null;
      let audioContext = null;
      let audioProcessor = null;
      let audioSource = null;
      let isAudioStreaming = false;
      let debugMode = true; // Enable detailed debugging
      let debugLogElement = null;

      function logDebug(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${message}`;

        if (debugMode) {
          console.log(`[AUDIO DEBUG] ${message}`, data || "");
        }

        // Also log to UI
        if (!debugLogElement) {
          debugLogElement = document.getElementById("audioDebugStatus");
        }

        if (debugLogElement) {
          const logEntry = data
            ? `${logMessage}\n${JSON.stringify(data, null, 2)}\n\n`
            : `${logMessage}\n\n`;
          debugLogElement.textContent += logEntry;
          debugLogElement.scrollTop = debugLogElement.scrollHeight;
        }
      }

      // Clear debug log
      function clearDebugLog() {
        if (debugLogElement) {
          debugLogElement.textContent = "";
        }
      }

      // Test audio system without WebSocket
      async function testAudioSystem() {
        clearDebugLog();
        logDebug("=== AUDIO SYSTEM TEST STARTED ===");

        try {
          // Test 1: Check browser support
          logDebug("Test 1: Browser Support Check");
          const hasGetUserMedia = !!(
            navigator.mediaDevices && navigator.mediaDevices.getUserMedia
          );
          const hasAudioContext = !!(
            window.AudioContext || window.webkitAudioContext
          );
          logDebug("Browser Support Results", {
            getUserMedia: hasGetUserMedia,
            audioContext: hasAudioContext,
            userAgent: navigator.userAgent,
          });

          if (!hasGetUserMedia || !hasAudioContext) {
            logDebug("ERROR: Missing required browser features");
            return false;
          }

          // Test 2: Request microphone access
          logDebug("Test 2: Microphone Access");
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              sampleRate: 16000,
              channelCount: 1,
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
            },
          });
          logDebug("Microphone access granted", {
            streamId: stream.id,
            tracks: stream.getAudioTracks().map((t) => ({
              id: t.id,
              kind: t.kind,
              label: t.label,
              enabled: t.enabled,
              readyState: t.readyState,
              settings: t.getSettings ? t.getSettings() : "not supported",
            })),
          });

          // Test 3: Create AudioContext
          logDebug("Test 3: AudioContext Creation");
          const AudioContextClass =
            window.AudioContext || window.webkitAudioContext;
          const audioContext = new AudioContextClass({ sampleRate: 16000 });
          logDebug("AudioContext created", {
            sampleRate: audioContext.sampleRate,
            state: audioContext.state,
            baseLatency: audioContext.baseLatency,
            outputLatency: audioContext.outputLatency,
            expectedSampleRate: 16000,
            sampleRateMatch: audioContext.sampleRate === 16000,
          });

          // Warn if sample rate doesn't match expected
          if (audioContext.sampleRate !== 16000) {
            logDebug(
              `⚠️ WARNING: AudioContext sample rate is ${audioContext.sampleRate}Hz, expected 16000Hz. This may cause transcription issues!`
            );
          }

          if (audioContext.state === "suspended") {
            logDebug("AudioContext suspended, attempting resume");
            await audioContext.resume();
            logDebug("AudioContext state after resume", {
              state: audioContext.state,
            });
          }

          // Test 4: Create audio processing chain
          logDebug("Test 4: Audio Processing Chain");
          const audioSource = audioContext.createMediaStreamSource(stream);
          const audioProcessor = audioContext.createScriptProcessor(4096, 1, 1);

          audioSource.connect(audioProcessor);
          audioProcessor.connect(audioContext.destination);

          let sampleCount = 0;
          audioProcessor.onaudioprocess = function (e) {
            if (sampleCount < 5) {
              // Only log first few samples
              const input = e.inputBuffer.getChannelData(0);
              let maxAmplitude = 0;
              for (let i = 0; i < input.length; i++) {
                maxAmplitude = Math.max(maxAmplitude, Math.abs(input[i]));
              }
              logDebug(`Audio sample ${sampleCount + 1}`, {
                inputLength: input.length,
                maxAmplitude: maxAmplitude.toFixed(4),
              });
              sampleCount++;
            }
          };

          // Test for 3 seconds
          await new Promise((resolve) => setTimeout(resolve, 3000));

          // Cleanup
          audioProcessor.disconnect();
          audioSource.disconnect();
          await audioContext.close();
          stream.getTracks().forEach((track) => track.stop());

          logDebug("=== AUDIO SYSTEM TEST COMPLETED SUCCESSFULLY ===");
          return true;
        } catch (err) {
          logDebug("AUDIO SYSTEM TEST FAILED", {
            error: err.name,
            message: err.message,
            stack: err.stack,
          });
          return false;
        }
      }

      // Check permissions
      async function checkPermissions() {
        clearDebugLog();
        logDebug("=== PERMISSION CHECK STARTED ===");

        try {
          // Check microphone permission
          if (navigator.permissions) {
            const micPermission = await navigator.permissions.query({
              name: "microphone",
            });
            logDebug("Microphone permission", { state: micPermission.state });
          } else {
            logDebug("Permissions API not supported");
          }

          // Check available audio devices
          if (
            navigator.mediaDevices &&
            navigator.mediaDevices.enumerateDevices
          ) {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputs = devices.filter(
              (device) => device.kind === "audioinput"
            );
            logDebug("Available audio input devices", {
              count: audioInputs.length,
              devices: audioInputs.map((d) => ({
                deviceId: d.deviceId,
                label: d.label || "Unknown",
                groupId: d.groupId,
              })),
            });
          }

          logDebug("=== PERMISSION CHECK COMPLETED ===");
        } catch (err) {
          logDebug("PERMISSION CHECK FAILED", {
            error: err.name,
            message: err.message,
          });
        }
      }

      function startAudioStreaming(targetLang) {
        if (isAudioStreaming) {
          logDebug("Already streaming, ignoring start request");
          return;
        }

        logDebug("Starting audio streaming", {
          targetLang,
          userAgent: navigator.userAgent,
        });
        isAudioStreaming = true;

        // Enhanced getUserMedia with better error handling
        const audioConstraints = {
          audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
        };

        logDebug("Requesting microphone access", audioConstraints);

        navigator.mediaDevices
          .getUserMedia(audioConstraints)
          .then((stream) => {
            logDebug("Microphone access granted", {
              streamId: stream.id,
              tracks: stream.getAudioTracks().map((t) => ({
                id: t.id,
                kind: t.kind,
                label: t.label,
                enabled: t.enabled,
                readyState: t.readyState,
                settings: t.getSettings ? t.getSettings() : "not supported",
              })),
            });

            audioStream = stream;

            // Create AudioContext with browser's default sample rate (we'll resample)
            try {
              // Try different AudioContext constructors for better browser compatibility
              const AudioContextClass =
                window.AudioContext || window.webkitAudioContext;
              audioContext = new AudioContextClass();

              logDebug("AudioContext created", {
                sampleRate: audioContext.sampleRate,
                state: audioContext.state,
                baseLatency: audioContext.baseLatency,
                outputLatency: audioContext.outputLatency,
                targetSampleRate: 16000,
                needsResampling: audioContext.sampleRate !== 16000,
              });

              // Resume context if it's suspended (Safari requirement)
              if (audioContext.state === "suspended") {
                logDebug("AudioContext is suspended, attempting to resume");
                audioContext
                  .resume()
                  .then(() => {
                    logDebug("AudioContext resumed successfully");
                  })
                  .catch((err) => {
                    logDebug("Failed to resume AudioContext", err);
                  });
              }
            } catch (err) {
              logDebug("Failed to create AudioContext", err);
              throw err;
            }

            // Create audio processing chain
            audioSource = audioContext.createMediaStreamSource(stream);
            logDebug("MediaStreamSource created", {
              numberOfInputs: audioSource.numberOfInputs,
              numberOfOutputs: audioSource.numberOfOutputs,
            });

            // Use createScriptProcessor (deprecated but more compatible) or try AudioWorklet
            try {
              audioProcessor = audioContext.createScriptProcessor(4096, 1, 1);
              logDebug("ScriptProcessor created with buffer size 4096");
            } catch (err) {
              logDebug("Failed to create ScriptProcessor", err);
              throw err;
            }

            // Connect the audio processing chain
            audioSource.connect(audioProcessor);
            audioProcessor.connect(audioContext.destination);
            logDebug("Audio processing chain connected");

            // Close existing WebSocket if any
            if (audioSocket && audioSocket.readyState !== WebSocket.CLOSED) {
              logDebug("Closing existing WebSocket connection");
              audioSocket.close();
            }

            // Connect to backend WebSocket for translation
            const wsUrl = `ws://127.0.0.1:8001/ws/${targetLang}`;
            logDebug("Connecting to WebSocket", { wsUrl });

            audioSocket = new WebSocket(wsUrl);
            audioSocket.binaryType = "arraybuffer";

            let audioDataSentCount = 0;
            let totalBytesSent = 0;

            // Volume monitoring variables
            let volumeSamples = [];
            let lowVolumeWarningShown = false;
            let speechDetectedCount = 0;
            let silenceCount = 0;

            audioSocket.onopen = () => {
              logDebug("Audio WebSocket connected successfully");

              // Send join-room message
              const joinMessage = {
                type: "join-room",
                roomId: document.getElementById("roomId").value,
                role: "preacher",
                audioConfig: {
                  sampleRate: audioContext.sampleRate,
                  channelCount: 1,
                  encoding: "pcm16",
                },
              };
              audioSocket.send(JSON.stringify(joinMessage));
              logDebug("Sent join-room message", joinMessage);
            };

            audioSocket.onclose = (event) => {
              logDebug("Audio WebSocket closed", {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean,
              });

              if (
                event.code === 1008 &&
                event.reason.includes("Too many concurrent connections")
              ) {
                logDebug(
                  "⚠️ Too many concurrent connections. Retrying in 2 seconds..."
                );
                setTimeout(() => {
                  if (isAudioStreaming) {
                    logDebug("Retrying WebSocket connection...");
                    startAudioStreaming(targetLang);
                  }
                }, 2000);
              } else {
                stopAudioStreaming();
              }
            };

            audioSocket.onerror = (err) => {
              logDebug("Audio WebSocket error", err);
              stopAudioStreaming();
            };

            audioSocket.onmessage = (event) => {
              logDebug("Received message from WebSocket", {
                type: typeof event.data,
                size: event.data.byteLength || event.data.length,
              });
            };

            // Audio processing event handler
            audioProcessor.onaudioprocess = function (e) {
              if (
                !isAudioStreaming ||
                audioSocket.readyState !== WebSocket.OPEN
              ) {
                return;
              }

              const input = e.inputBuffer.getChannelData(0);

              // Validate input data
              if (!input || input.length === 0) {
                logDebug("No audio input data received");
                return;
              }

              // Enhanced volume analysis and monitoring
              let maxAmplitude = 0;
              let avgAmplitude = 0;
              let rmsAmplitude = 0;
              let sumSquares = 0;

              // Calculate comprehensive volume metrics
              for (let i = 0; i < input.length; i++) {
                const sample = Math.abs(input[i]);
                maxAmplitude = Math.max(maxAmplitude, sample);
                avgAmplitude += sample;
                sumSquares += sample * sample;
              }
              avgAmplitude /= input.length;
              rmsAmplitude = Math.sqrt(sumSquares / input.length);

              // Track volume over time
              volumeSamples.push(maxAmplitude);
              if (volumeSamples.length > 50) {
                volumeSamples.shift(); // Keep last 50 samples
              }

              // Volume thresholds for speech detection
              const SPEECH_THRESHOLD = 0.01; // Minimum for speech
              const RECOMMENDED_MIN = 0.05; // Recommended minimum
              const GOOD_SPEECH_LEVEL = 0.1; // Good speech level

              // Determine audio quality
              const isSpeechLevel = maxAmplitude > SPEECH_THRESHOLD;
              const isGoodLevel = maxAmplitude > RECOMMENDED_MIN;
              const isOptimalLevel = maxAmplitude > GOOD_SPEECH_LEVEL;

              if (isSpeechLevel) {
                speechDetectedCount++;
              } else {
                silenceCount++;
              }

              // Calculate average volume over recent samples
              const recentAvgVolume =
                volumeSamples.length > 0
                  ? volumeSamples.reduce((a, b) => a + b, 0) /
                    volumeSamples.length
                  : 0;

              // Show volume warnings
              if (!lowVolumeWarningShown && audioDataSentCount > 50) {
                if (recentAvgVolume < SPEECH_THRESHOLD) {
                  logDebug(
                    "🔇 WARNING: Audio volume is extremely low! Speech may not be detected.",
                    {
                      avgVolume: recentAvgVolume.toFixed(4),
                      threshold: SPEECH_THRESHOLD,
                      suggestion:
                        "Please speak louder or check microphone settings",
                    }
                  );
                  lowVolumeWarningShown = true;
                } else if (recentAvgVolume < RECOMMENDED_MIN) {
                  logDebug(
                    "🔉 WARNING: Audio volume is low. Consider speaking louder for better recognition.",
                    {
                      avgVolume: recentAvgVolume.toFixed(4),
                      recommended: RECOMMENDED_MIN,
                    }
                  );
                  lowVolumeWarningShown = true;
                }
              }

              // Resample to 16kHz if needed and convert to Int16 PCM
              let processedSamples = input;
              const currentSampleRate = audioContext.sampleRate;
              const targetSampleRate = 16000;

              if (currentSampleRate !== targetSampleRate) {
                // Simple resampling - take every nth sample
                const ratio = currentSampleRate / targetSampleRate;
                const outputLength = Math.floor(input.length / ratio);
                const resampled = new Float32Array(outputLength);

                for (let i = 0; i < outputLength; i++) {
                  const sourceIndex = Math.floor(i * ratio);
                  resampled[i] = input[sourceIndex];
                }
                processedSamples = resampled;
              }

              // Convert Float32 [-1,1] to Int16 PCM
              const pcm = new Int16Array(processedSamples.length);
              for (let i = 0; i < processedSamples.length; i++) {
                let s = Math.max(-1, Math.min(1, processedSamples[i]));
                pcm[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
              }

              // Send audio data
              try {
                audioSocket.send(pcm.buffer);
                audioDataSentCount++;
                totalBytesSent += pcm.buffer.byteLength;

                // Enhanced logging with volume analysis
                if (audioDataSentCount % 100 === 0) {
                  const speechPercentage = (
                    (speechDetectedCount /
                      (speechDetectedCount + silenceCount)) *
                    100
                  ).toFixed(1);
                  const volumeStatus = isOptimalLevel
                    ? "🟢 OPTIMAL"
                    : isGoodLevel
                    ? "🟡 GOOD"
                    : isSpeechLevel
                    ? "🟠 LOW"
                    : "🔴 TOO LOW";

                  logDebug(`🎤 Audio streaming progress`, {
                    packetsCount: audioDataSentCount,
                    totalBytes: totalBytesSent,
                    lastPacketSize: pcm.buffer.byteLength,
                    volumeStatus: volumeStatus,
                    maxAmplitude: maxAmplitude.toFixed(4),
                    avgAmplitude: avgAmplitude.toFixed(4),
                    rmsAmplitude: rmsAmplitude.toFixed(4),
                    recentAvgVolume: recentAvgVolume.toFixed(4),
                    speechPercentage: speechPercentage + "%",
                    speechDetected: speechDetectedCount,
                    silenceCount: silenceCount,
                    inputSampleRate: audioContext.sampleRate,
                    outputSampleRate: 16000,
                    isResampling: audioContext.sampleRate !== 16000,
                    inputLength: input.length,
                    outputLength: pcm.length,
                    volumeHistory: volumeSamples
                      .slice(-5)
                      .map((v) => v.toFixed(3)), // Last 5 samples
                  });

                  // Additional warnings for persistent issues
                  if (audioDataSentCount >= 500 && speechPercentage < 10) {
                    logDebug(
                      "⚠️ ALERT: Very little speech detected. Possible issues:",
                      {
                        suggestions: [
                          "Check if microphone is muted or very quiet",
                          "Try speaking closer to the microphone",
                          "Verify microphone permissions are granted",
                          "Check browser's microphone settings",
                          "Test microphone in other applications",
                        ],
                      }
                    );
                  }
                }
              } catch (err) {
                logDebug("Failed to send audio data", err);
                stopAudioStreaming();
              }
            };

            logDebug("Audio processing setup complete");
          })
          .catch((err) => {
            logDebug("Microphone access error", err);
            console.error("Microphone access error:", err);
            stopAudioStreaming();

            // Show more specific error messages
            let errorMessage = "Microphone access failed: ";
            if (err.name === "NotAllowedError") {
              errorMessage +=
                "Permission denied. Please allow microphone access and try again.";
            } else if (err.name === "NotFoundError") {
              errorMessage +=
                "No microphone found. Please connect a microphone and try again.";
            } else if (err.name === "NotReadableError") {
              errorMessage +=
                "Microphone is already in use by another application.";
            } else {
              errorMessage += err.message;
            }

            alert(errorMessage);
          });
      }

      function stopAudioStreaming() {
        logDebug("Stopping audio streaming");
        isAudioStreaming = false;

        if (audioProcessor) {
          audioProcessor.disconnect();
          audioProcessor.onaudioprocess = null;
          audioProcessor = null;
          logDebug("AudioProcessor disconnected");
        }

        if (audioSource) {
          audioSource.disconnect();
          audioSource = null;
          logDebug("AudioSource disconnected");
        }

        if (audioContext && audioContext.state !== "closed") {
          audioContext
            .close()
            .then(() => {
              logDebug("AudioContext closed");
            })
            .catch((err) => {
              logDebug("Error closing AudioContext", err);
            });
          audioContext = null;
        }

        if (audioStream) {
          audioStream.getTracks().forEach((track) => {
            track.stop();
            logDebug("Audio track stopped", {
              id: track.id,
              kind: track.kind,
              readyState: track.readyState,
            });
          });
          audioStream = null;
        }

        if (audioSocket) {
          if (audioSocket.readyState === WebSocket.OPEN) {
            audioSocket.close(1000, "Client stopping stream");
          }
          audioSocket = null;
          logDebug("AudioSocket closed");
        }

        logDebug("Audio streaming stopped completely");
      }

      // Hook up to broadcast button for demo
      const startBroadcastBtn = document.getElementById("startBroadcastBtn");
      let broadcastActive = false;

      // Add user interaction trigger for Safari AudioContext activation
      async function initAudioContextForSafari() {
        if (!window.AudioContext && !window.webkitAudioContext) {
          logDebug("No AudioContext support detected");
          return false;
        }

        try {
          // Create a temporary context to test user interaction requirement
          const AudioContextClass =
            window.AudioContext || window.webkitAudioContext;
          const tempContext = new AudioContextClass();

          if (tempContext.state === "suspended") {
            logDebug(
              "AudioContext requires user interaction (Safari), attempting resume"
            );
            await tempContext.resume();
          }

          await tempContext.close();
          logDebug("AudioContext user interaction test passed");
          return true;
        } catch (err) {
          logDebug("AudioContext user interaction test failed", err);
          return false;
        }
      }

      startBroadcastBtn.addEventListener("click", async () => {
        const targetLang = document.getElementById("languageSelect").value;

        if (!broadcastActive) {
          // Test AudioContext activation first (Safari requirement)
          const audioContextReady = await initAudioContextForSafari();
          if (!audioContextReady) {
            alert(
              "Audio context initialization failed. This may be a browser compatibility issue."
            );
            return;
          }

          startAudioStreaming(targetLang);
          broadcastActive = true;
          startBroadcastBtn.textContent = "⏹️ Stop Broadcasting";
        } else {
          stopAudioStreaming();
          broadcastActive = false;
          startBroadcastBtn.textContent = "🚀 Start Broadcasting Sermon";
        }
      });

      // Add microphone test functionality
      let micTestActive = false;

      function testMicrophone() {
        const testBtn = document.getElementById("testMicBtn");
        const testResult = document.getElementById("micTestResult");

        if (micTestActive) {
          // Stop test
          micTestActive = false;
          testBtn.textContent = "Test Microphone";
          testBtn.classList.remove("btn-danger");
          testBtn.classList.add("btn-secondary");
          testResult.innerHTML = "";

          if (testAudioContext) {
            testAudioContext.close();
            testAudioContext = null;
          }
          if (testStream) {
            testStream.getTracks().forEach((track) => track.stop());
            testStream = null;
          }
          return;
        }

        // Start test
        micTestActive = true;
        testBtn.textContent = "Stop Test";
        testBtn.classList.remove("btn-secondary");
        testBtn.classList.add("btn-danger");
        testResult.innerHTML =
          "<div class='text-info'>🎤 Testing microphone... Speak into your microphone</div>";

        let testAudioContext, testStream, testSource, testProcessor;
        let testSamples = [];
        let testStartTime = Date.now();

        navigator.mediaDevices
          .getUserMedia({
            audio: {
              sampleRate: 16000,
              channelCount: 1,
              echoCancellation: true,
              noiseSuppression: true,
            },
          })
          .then((stream) => {
            if (!micTestActive) return;

            testStream = stream;
            testAudioContext = new (window.AudioContext ||
              window.webkitAudioContext)();
            testSource = testAudioContext.createMediaStreamSource(stream);
            testProcessor = testAudioContext.createScriptProcessor(4096, 1, 1);

            testProcessor.onaudioprocess = function (e) {
              if (!micTestActive) return;

              const input = e.inputBuffer.getChannelData(0);
              let maxAmplitude = 0;

              for (let i = 0; i < input.length; i++) {
                maxAmplitude = Math.max(maxAmplitude, Math.abs(input[i]));
              }

              testSamples.push(maxAmplitude);

              // Show real-time feedback
              const elapsed = (Date.now() - testStartTime) / 1000;
              const avgVolume =
                testSamples.reduce((a, b) => a + b, 0) / testSamples.length;
              const maxVolume = Math.max(...testSamples);

              let status, color, advice;
              if (maxVolume > 0.1) {
                status = "🟢 EXCELLENT";
                color = "text-success";
                advice = "Perfect volume level for speech recognition!";
              } else if (maxVolume > 0.05) {
                status = "🟡 GOOD";
                color = "text-warning";
                advice = "Good volume, should work well.";
              } else if (maxVolume > 0.01) {
                status = "🟠 LOW";
                color = "text-warning";
                advice =
                  "Volume is low. Try speaking louder or moving closer to the microphone.";
              } else {
                status = "🔴 TOO LOW";
                color = "text-danger";
                advice =
                  "Volume too low! Check microphone settings and permissions.";
              }

              testResult.innerHTML = `
              <div class="${color}">
                <strong>${status}</strong><br>
                Time: ${elapsed.toFixed(1)}s<br>
                Current: ${maxAmplitude.toFixed(4)}<br>
                Average: ${avgVolume.toFixed(4)}<br>
                Peak: ${maxVolume.toFixed(4)}<br>
                Sample Rate: ${testAudioContext.sampleRate}Hz<br>
                <em>${advice}</em>
              </div>
            `;

              // Auto-stop after 10 seconds
              if (elapsed > 10) {
                testMicrophone(); // Stop test
              }
            };

            testSource.connect(testProcessor);
            testProcessor.connect(testAudioContext.destination);
          })
          .catch((err) => {
            micTestActive = false;
            testBtn.textContent = "Test Microphone";
            testBtn.classList.remove("btn-danger");
            testBtn.classList.add("btn-secondary");
            testResult.innerHTML = `<div class='text-danger'>❌ Microphone test failed: ${err.message}</div>`;
          });
      }
    </script>
  </body>
</html>
