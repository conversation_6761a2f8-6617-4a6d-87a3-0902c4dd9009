# 🎥 Video Streaming Backend API Implementation Guide

## 📋 Overview

This document provides complete examples of what your backend will receive from the video streaming client and how to implement the WebSocket video distribution service.

---

## 🌐 WebSocket Endpoint

**URL**: `ws://127.0.0.1:8002/video/{roomId}`

**Example**: `ws://127.0.0.1:8002/video/meeting-123`

---

## 📥 **What Backend Will Receive**

### **1. Initial Connection Message (JSON)**

When a client connects, you'll receive this JSON message:

```json
{
  "type": "join-room",
  "roomId": "meeting-123",
  "role": "preacher", // or "listener"
  "videoConfig": {
    "width": 1280,
    "height": 720,
    "frameRate": 30,
    "quality": 0.8
  }
}
```

### **2. Video Frame Data (Binary)**

For each frame (30 FPS), you'll receive binary data in this format:

```
[4 bytes] Metadata Length (Uint32)
[N bytes] Metadata JSON
[X bytes] JPEG Frame Data
```

**Metadata JSON Example**:

```json
{
  "timestamp": 1703123456789,
  "width": 1280,
  "height": 720,
  "quality": 0.8,
  "frameNumber": 1847
}
```

**Frame Data**: Raw JPEG bytes (~50-100KB per frame)

---

## 🐍 **Python Backend Implementation Example**

### **FastAPI WebSocket Handler**

```python
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from typing import Dict, List, Set
import json
import asyncio
import struct
from datetime import datetime

app = FastAPI()

# Room management
class VideoRoom:
    def __init__(self, room_id: str):
        self.room_id = room_id
        self.speaker: WebSocket | None = None
        self.viewers: Set[WebSocket] = set()
        self.last_frame_time = 0
        self.frame_count = 0

    async def add_speaker(self, websocket: WebSocket):
        if self.speaker:
            await self.speaker.close(code=1000, reason="New speaker joined")
        self.speaker = websocket
        print(f"Speaker joined room {self.room_id}")

    async def add_viewer(self, websocket: WebSocket):
        self.viewers.add(websocket)
        print(f"Viewer joined room {self.room_id}. Total viewers: {len(self.viewers)}")

    async def remove_client(self, websocket: WebSocket):
        if self.speaker == websocket:
            self.speaker = None
            print(f"Speaker left room {self.room_id}")
        else:
            self.viewers.discard(websocket)
            print(f"Viewer left room {self.room_id}. Remaining viewers: {len(self.viewers)}")

    async def broadcast_frame(self, frame_data: bytes, metadata: dict):
        """Broadcast frame to all viewers"""
        if not self.viewers:
            return

        self.frame_count += 1
        current_time = datetime.now().timestamp() * 1000

        # Log stats every 30 frames (1 second at 30fps)
        if self.frame_count % 30 == 0:
            fps = 30000 / (current_time - self.last_frame_time) if self.last_frame_time else 0
            print(f"Room {self.room_id}: Frame {self.frame_count}, "
                  f"Size: {len(frame_data)}KB, FPS: {fps:.1f}, Viewers: {len(self.viewers)}")
            self.last_frame_time = current_time

        # Send to all viewers
        disconnected_viewers = []
        for viewer in self.viewers:
            try:
                await viewer.send_bytes(frame_data)
            except Exception as e:
                print(f"Failed to send frame to viewer: {e}")
                disconnected_viewers.append(viewer)

        # Remove disconnected viewers
        for viewer in disconnected_viewers:
            self.viewers.discard(viewer)

# Global room storage
rooms: Dict[str, VideoRoom] = {}

def get_or_create_room(room_id: str) -> VideoRoom:
    if room_id not in rooms:
        rooms[room_id] = VideoRoom(room_id)
    return rooms[room_id]

@app.websocket("/video/{room_id}")
async def websocket_endpoint(websocket: WebSocket, room_id: str):
    await websocket.accept()

    room = get_or_create_room(room_id)
    client_role = None

    try:
        while True:
            # Receive data
            data = await websocket.receive()

            if 'text' in data:
                # Handle JSON messages
                message = json.loads(data['text'])

                if message['type'] == 'join-room':
                    client_role = message.get('role', 'listener')
                    video_config = message.get('videoConfig', {})

                    print(f"Client joining as {client_role} with config: {video_config}")

                    if client_role == 'preacher':
                        await room.add_speaker(websocket)
                    else:
                        await room.add_viewer(websocket)

                    # Send confirmation
                    await websocket.send_text(json.dumps({
                        "type": "joined",
                        "role": client_role,
                        "roomId": room_id,
                        "viewerCount": len(room.viewers)
                    }))

            elif 'bytes' in data:
                # Handle binary frame data
                frame_data = data['bytes']

                if client_role == 'preacher' and len(frame_data) > 4:
                    # Parse frame data
                    metadata_length = struct.unpack('<I', frame_data[:4])[0]

                    if len(frame_data) >= 4 + metadata_length:
                        metadata_bytes = frame_data[4:4 + metadata_length]
                        jpeg_data = frame_data[4 + metadata_length:]

                        try:
                            metadata = json.loads(metadata_bytes.decode('utf-8'))

                            # Broadcast to all viewers in the room
                            await room.broadcast_frame(frame_data, metadata)

                        except json.JSONDecodeError as e:
                            print(f"Failed to parse metadata: {e}")

    except WebSocketDisconnect:
        print(f"Client disconnected from room {room_id}")
    except Exception as e:
        print(f"Error in websocket: {e}")
    finally:
        if room:
            await room.remove_client(websocket)

            # Clean up empty rooms
            if not room.speaker and not room.viewers:
                del rooms[room_id]
                print(f"Room {room_id} deleted (empty)")

# Health check endpoint
@app.get("/health")
def health_check():
    return {
        "status": "ok",
        "rooms": len(rooms),
        "total_connections": sum(
            (1 if room.speaker else 0) + len(room.viewers)
            for room in rooms.values()
        )
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8002)
```

---

## 🔧 **Frame Processing Example**

### **Parse Incoming Frame Data**

```python
def parse_video_frame(frame_data: bytes):
    """Parse video frame data from client"""

    if len(frame_data) < 4:
        raise ValueError("Invalid frame data: too short")

    # Read metadata length (first 4 bytes)
    metadata_length = struct.unpack('<I', frame_data[:4])[0]

    if len(frame_data) < 4 + metadata_length:
        raise ValueError("Invalid frame data: metadata incomplete")

    # Extract metadata
    metadata_bytes = frame_data[4:4 + metadata_length]
    metadata = json.loads(metadata_bytes.decode('utf-8'))

    # Extract JPEG data
    jpeg_data = frame_data[4 + metadata_length:]

    return {
        'metadata': metadata,
        'jpeg_data': jpeg_data,
        'frame_size': len(jpeg_data)
    }

# Example usage
frame_info = parse_video_frame(received_data)
print(f"Frame {frame_info['metadata']['frameNumber']}: "
      f"{frame_info['frame_size']} bytes, "
      f"{frame_info['metadata']['width']}x{frame_info['metadata']['height']}")
```

### **Optional: Frame Processing/Optimization**

```python
from PIL import Image
import io

def process_frame(jpeg_data: bytes, target_quality: int = 70):
    """Optionally recompress/resize frames"""

    # Load JPEG
    image = Image.open(io.BytesIO(jpeg_data))

    # Resize if needed (e.g., for mobile clients)
    # image = image.resize((640, 360), Image.Resampling.LANCZOS)

    # Recompress with different quality
    output = io.BytesIO()
    image.save(output, format='JPEG', quality=target_quality, optimize=True)

    return output.getvalue()
```

---

## 📊 **Expected Data Volume**

### **Bandwidth Requirements**

| Quality | Resolution | FPS | Frame Size | Bandwidth per Client |
| ------- | ---------- | --- | ---------- | -------------------- |
| Low     | 640x360    | 15  | ~15KB      | ~1.8 Mbps            |
| Medium  | 1280x720   | 30  | ~50KB      | ~12 Mbps             |
| High    | 1920x1080  | 30  | ~100KB     | ~24 Mbps             |

### **Server Load Example**

- **1 Speaker + 10 Viewers**: ~120 Mbps download, ~12 Mbps upload
- **1 Speaker + 100 Viewers**: ~1.2 Gbps download, ~12 Mbps upload

---

## 🚀 **Production Optimizations**

### **1. Frame Buffering**

```python
import asyncio
from collections import deque

class FrameBuffer:
    def __init__(self, max_size: int = 5):
        self.frames = deque(maxlen=max_size)
        self.lock = asyncio.Lock()

    async def add_frame(self, frame_data: bytes):
        async with self.lock:
            self.frames.append(frame_data)

    async def get_latest_frame(self) -> bytes | None:
        async with self.lock:
            return self.frames[-1] if self.frames else None
```

### **2. Adaptive Quality**

```python
def adjust_quality_based_on_viewers(viewer_count: int) -> float:
    """Reduce quality when many viewers"""
    if viewer_count > 50:
        return 0.6  # Lower quality
    elif viewer_count > 20:
        return 0.7  # Medium quality
    else:
        return 0.8  # High quality
```

### **3. Connection Management**

```python
import asyncio
import weakref

class ConnectionManager:
    def __init__(self):
        self.connections: Set[WebSocket] = set()

    async def add_connection(self, websocket: WebSocket):
        self.connections.add(websocket)

    async def remove_connection(self, websocket: WebSocket):
        self.connections.discard(websocket)

    async def broadcast_to_all(self, data: bytes):
        if not self.connections:
            return

        # Send to all connections concurrently
        tasks = []
        for connection in self.connections.copy():
            tasks.append(self.safe_send(connection, data))

        await asyncio.gather(*tasks, return_exceptions=True)

    async def safe_send(self, websocket: WebSocket, data: bytes):
        try:
            await websocket.send_bytes(data)
        except Exception:
            # Remove failed connection
            self.connections.discard(websocket)
```

---

## 🧪 **Testing the Implementation**

### **Test WebSocket Connection**

```python
import asyncio
import websockets
import json

async def test_video_websocket():
    uri = "ws://127.0.0.1:8002/video/test-room"

    async with websockets.connect(uri) as websocket:
        # Send join message
        join_message = {
            "type": "join-room",
            "roomId": "test-room",
            "role": "listener",
            "videoConfig": {
                "width": 1280,
                "height": 720,
                "frameRate": 30,
                "quality": 0.8
            }
        }

        await websocket.send(json.dumps(join_message))

        # Listen for responses
        while True:
            try:
                data = await websocket.recv()
                if isinstance(data, str):
                    print(f"Received message: {data}")
                else:
                    print(f"Received frame: {len(data)} bytes")
            except Exception as e:
                print(f"Error: {e}")
                break

# Run test
asyncio.run(test_video_websocket())
```

---

## 📈 **Monitoring & Logging**

### **Essential Metrics to Track**

```python
import time
from collections import defaultdict

class VideoMetrics:
    def __init__(self):
        self.rooms = defaultdict(lambda: {
            'frames_received': 0,
            'frames_sent': 0,
            'bytes_received': 0,
            'bytes_sent': 0,
            'viewers': 0,
            'last_frame_time': 0,
            'fps': 0
        })

    def record_frame(self, room_id: str, frame_size: int, viewer_count: int):
        room_stats = self.rooms[room_id]
        current_time = time.time()

        room_stats['frames_received'] += 1
        room_stats['frames_sent'] += viewer_count
        room_stats['bytes_received'] += frame_size
        room_stats['bytes_sent'] += frame_size * viewer_count
        room_stats['viewers'] = viewer_count

        # Calculate FPS
        if room_stats['last_frame_time']:
            time_diff = current_time - room_stats['last_frame_time']
            room_stats['fps'] = 1.0 / time_diff if time_diff > 0 else 0

        room_stats['last_frame_time'] = current_time

    def get_stats(self):
        return dict(self.rooms)

# Usage in your WebSocket handler
metrics = VideoMetrics()

# In your broadcast_frame method:
metrics.record_frame(room_id, len(frame_data), len(viewers))
```

---

## 🔒 **Security Considerations**

### **Rate Limiting**

```python
import time
from collections import defaultdict

class RateLimiter:
    def __init__(self, max_fps: int = 35):
        self.max_fps = max_fps
        self.client_frames = defaultdict(list)

    def allow_frame(self, client_id: str) -> bool:
        now = time.time()
        frames = self.client_frames[client_id]

        # Remove old frames (older than 1 second)
        frames[:] = [t for t in frames if now - t < 1.0]

        if len(frames) >= self.max_fps:
            return False

        frames.append(now)
        return True
```

### **Frame Size Validation**

```python
MAX_FRAME_SIZE = 200 * 1024  # 200KB max per frame

def validate_frame(frame_data: bytes) -> bool:
    if len(frame_data) > MAX_FRAME_SIZE:
        return False

    # Validate JPEG header
    if len(frame_data) < 10:
        return False

    # Check for JPEG magic bytes
    if frame_data[:2] != b'\xff\xd8':
        return False

    return True
```

---

This implementation provides a robust foundation for handling video streaming from your frontend clients. The backend will receive precisely the data formats shown above and can distribute video frames to all connected viewers in real-time! 🎥✨
