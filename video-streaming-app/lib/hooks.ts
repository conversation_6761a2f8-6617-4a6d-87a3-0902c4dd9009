import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiClient } from './api';
import { 
  User<PERSON>ogin, 
  User<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>oin, 
  SpeakerLoginRequest,
  RoomCreateRequest,
  RoomJoinRequest,
  RoomLeaveRequest,
  RoomChatRequest 
} from './types';

// Authentication hooks
export function useLogin() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UserLogin) => apiClient.login(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth', 'status'] });
      queryClient.invalidateQueries({ queryKey: ['user', 'current'] });
    },
  });
}

export function useRegister() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UserRegister) => apiClient.register(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth', 'status'] });
      queryClient.invalidateQueries({ queryKey: ['user', 'current'] });
    },
  });
}

export function useGuestJoin() {
  return useMutation({
    mutationFn: (data: GuestJoin) => apiClient.guestJoin(data),
  });
}

export function useSpeakerLogin() {
  return useMutation({
    mutationFn: (data: SpeakerLoginRequest) => apiClient.speakerLogin(data),
  });
}

export function useLogout() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => apiClient.logout(),
    onSuccess: () => {
      queryClient.clear();
    },
  });
}

// User hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: ['user', 'current'],
    queryFn: () => apiClient.getCurrentUser(),
    retry: false,
  });
}

export function useAuthStatus() {
  return useQuery({
    queryKey: ['auth', 'status'],
    queryFn: () => apiClient.getAuthStatus(),
    retry: false,
  });
}

// Room hooks
export function useCreateRoom() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: RoomCreateRequest) => apiClient.createRoom(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rooms'] });
    },
  });
}

export function useJoinRoom() {
  return useMutation({
    mutationFn: (data: RoomJoinRequest) => apiClient.joinRoom(data),
  });
}

export function useLeaveRoom() {
  return useMutation({
    mutationFn: (data: RoomLeaveRequest) => apiClient.leaveRoom(data),
  });
}

export function useRoomMetadata(roomId: number) {
  return useQuery({
    queryKey: ['room', 'metadata', roomId],
    queryFn: () => apiClient.getRoomMetadata(roomId),
    enabled: !!roomId,
  });
}

export function useListRooms() {
  return useQuery({
    queryKey: ['rooms'],
    queryFn: () => apiClient.listRooms(),
  });
}

// Chat hooks
export function useRoomChat() {
  return useQuery({
    queryKey: ['room', 'chat'],
    queryFn: () => apiClient.getRoomChat(),
  });
}

export function usePostRoomChat() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: RoomChatRequest) => apiClient.postRoomChat(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['room', 'chat'] });
    },
  });
}

// Health check hook
export function useHealthCheck() {
  return useQuery({
    queryKey: ['health'],
    queryFn: () => apiClient.healthCheck(),
    refetchInterval: 30000, // Check every 30 seconds
  });
}
