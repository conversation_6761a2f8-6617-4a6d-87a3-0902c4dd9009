import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "./api";
import {
  User<PERSON>og<PERSON>,
  User<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>oin,
  SpeakerLoginRequest,
  RoomCreateRequest,
  RoomJoinRequest,
  RoomLeaveRequest,
  RoomChatRequest,
  MeetingCreate,
  MeetingUpdate,
  MeetingLanguageCreate,
  MeetingJoin,
  ChatMessageInc,
} from "./types";

// Authentication hooks
export function useLogin() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UserLogin) => apiClient.login(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["auth", "status"] });
      queryClient.invalidateQueries({ queryKey: ["user", "current"] });
    },
  });
}

export function useRegister() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UserRegister) => apiClient.register(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["auth", "status"] });
      queryClient.invalidateQueries({ queryKey: ["user", "current"] });
    },
  });
}

export function useGuestJoin() {
  return useMutation({
    mutationFn: (data: GuestJoin) => apiClient.guestJoin(data),
  });
}

export function useSpeakerLogin() {
  return useMutation({
    mutationFn: (data: SpeakerLoginRequest) => apiClient.speakerLogin(data),
  });
}

export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => apiClient.logout(),
    onSuccess: () => {
      queryClient.clear();
    },
  });
}

// User hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: ["user", "current"],
    queryFn: () => apiClient.getCurrentUser(),
    retry: false,
  });
}

export function useAuthStatus() {
  return useQuery({
    queryKey: ["auth", "status"],
    queryFn: () => apiClient.getAuthStatus(),
    retry: false,
  });
}

// Room hooks
export function useCreateRoom() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RoomCreateRequest) => apiClient.createRoom(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["rooms"] });
    },
  });
}

export function useJoinRoom() {
  return useMutation({
    mutationFn: (data: RoomJoinRequest) => apiClient.joinRoom(data),
  });
}

export function useLeaveRoom() {
  return useMutation({
    mutationFn: (data: RoomLeaveRequest) => apiClient.leaveRoom(data),
  });
}

export function useRoomMetadata(roomId: number) {
  return useQuery({
    queryKey: ["room", "metadata", roomId],
    queryFn: () => apiClient.getRoomMetadata(roomId),
    enabled: !!roomId,
  });
}

export function useListRooms() {
  return useQuery({
    queryKey: ["rooms"],
    queryFn: () => apiClient.listRooms(),
  });
}

// Chat hooks
export function useRoomChat() {
  return useQuery({
    queryKey: ["room", "chat"],
    queryFn: () => apiClient.getRoomChat(),
  });
}

export function usePostRoomChat() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RoomChatRequest) => apiClient.postRoomChat(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room", "chat"] });
    },
  });
}

// Health check hook
export function useHealthCheck() {
  return useQuery({
    queryKey: ["health"],
    queryFn: () => apiClient.healthCheck(),
    refetchInterval: 30000, // Check every 30 seconds
  });
}

// New Meeting Management Hooks (aligned with backend)
export function useCreateMeeting() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: MeetingCreate) => apiClient.createMeeting(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["meetings"] });
    },
  });
}

export function useMyMeetings() {
  return useQuery({
    queryKey: ["meetings", "my-meetings"],
    queryFn: () => apiClient.getMyMeetings(),
  });
}

export function useMeeting(id: number) {
  return useQuery({
    queryKey: ["meetings", id],
    queryFn: () => apiClient.getMeeting(id),
    enabled: !!id,
  });
}

export function useUpdateMeeting() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: MeetingUpdate }) =>
      apiClient.updateMeeting(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["meetings", id] });
      queryClient.invalidateQueries({ queryKey: ["meetings"] });
    },
  });
}

export function useAddLanguageToMeeting() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      meetingId,
      data,
    }: {
      meetingId: number;
      data: MeetingLanguageCreate;
    }) => apiClient.addLanguageToMeeting(meetingId, data),
    onSuccess: (_, { meetingId }) => {
      queryClient.invalidateQueries({
        queryKey: ["meetings", meetingId, "languages"],
      });
      queryClient.invalidateQueries({ queryKey: ["meetings", meetingId] });
    },
  });
}

export function useMeetingLanguages(meetingId: number) {
  return useQuery({
    queryKey: ["meetings", meetingId, "languages"],
    queryFn: () => apiClient.getMeetingLanguages(meetingId),
    enabled: !!meetingId,
  });
}

export function useJoinMeeting() {
  return useMutation({
    mutationFn: (data: MeetingJoin) => apiClient.joinMeeting(data),
  });
}

export function useMeetingByCode(code: string) {
  return useQuery({
    queryKey: ["meetings", "code", code],
    queryFn: () => apiClient.getMeetingByCode(code),
    enabled: !!code,
  });
}

export function usePostChatMessage() {
  return useMutation({
    mutationFn: (data: ChatMessageInc) => apiClient.postChatMessage(data),
  });
}
