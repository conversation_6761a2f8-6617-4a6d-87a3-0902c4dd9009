import { WebSocketMessage, LanguageCode } from "./types";

export interface VideoConfig {
  width: number;
  height: number;
  frameRate: number;
  quality: number; // JPEG quality 0.1-1.0 (for JPEG mode) or bitrate (for H.264)
  useH264: boolean; // Use H.264 encoding instead of JPEG
}

export interface VideoStats {
  framesSent: number;
  totalBytes: number;
  averageFrameSize: number;
  currentFPS: number;
  compression: number;
}

export class VideoStreamingClient {
  private videoStream: MediaStream | null = null;
  private videoSocket: WebSocket | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private videoElement: HTMLVideoElement | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private isStreaming = false;
  private frameInterval: number | null = null;

  // Stats tracking
  private framesSent = 0;
  private totalBytes = 0;
  private lastFrameTime = 0;
  private framesSentInLastSecond = 0;
  private lastStatsUpdate = 0;
  private currentFPS = 0;

  private config: VideoConfig = {
    width: 1280,
    height: 720,
    frameRate: 30,
    quality: 0.8,
    useH264: true, // Use H.264 by default for backend compatibility
  };

  private onStatusChange?: (status: {
    cameraStatus: string;
    wsStatus: string;
    streamingStatus: string;
    stats: VideoStats;
  }) => void;

  private onMessage?: (message: any) => void;
  private onError?: (error: string) => void;

  constructor(
    onStatusChange?: (status: any) => void,
    onMessage?: (message: any) => void,
    onError?: (error: string) => void,
    config?: Partial<VideoConfig>
  ) {
    this.onStatusChange = onStatusChange;
    this.onMessage = onMessage;
    this.onError = onError;

    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Create canvas for frame processing
    this.canvas = document.createElement("canvas");
    this.ctx = this.canvas.getContext("2d");
    this.videoElement = document.createElement("video");
    this.videoElement.muted = true;
    this.videoElement.playsInline = true;
  }

  async startStreaming(
    roomId: string = "default-room",
    role: "speaker" | "viewer" = "speaker"
  ): Promise<void> {
    if (this.isStreaming) {
      throw new Error("Already streaming");
    }

    try {
      // Request camera access
      this.updateStatus("Requesting camera access...", "warning", "camera");

      const constraints = {
        video: {
          width: { ideal: this.config.width },
          height: { ideal: this.config.height },
          frameRate: { ideal: this.config.frameRate },
          facingMode: "user",
        },
        audio: false, // Audio handled separately
      };

      this.videoStream = await navigator.mediaDevices.getUserMedia(constraints);
      this.updateStatus("Camera access granted", "success", "camera");

      // Setup video element
      this.videoElement!.srcObject = this.videoStream;
      await this.videoElement!.play();

      // Setup canvas dimensions
      const videoTrack = this.videoStream.getVideoTracks()[0];
      const settings = videoTrack.getSettings();

      this.canvas!.width = settings.width || this.config.width;
      this.canvas!.height = settings.height || this.config.height;

      // Connect to the main WebSocket (same as audio)
      this.updateStatus("Connecting to WebSocket...", "warning", "ws");
      const wsUrl = `ws://127.0.0.1:8001/ws/en/en`; // Use main translation WebSocket
      this.videoSocket = new WebSocket(wsUrl);
      this.videoSocket.binaryType = "arraybuffer";

      this.videoSocket.onopen = () => {
        this.updateStatus("WebSocket connected", "success", "ws");

        // Send start_recording message for speakers
        if (role === "speaker") {
          const startRecordingMessage = {
            type: "start_recording",
            width: this.config.width,
            height: this.config.height,
          };
          this.videoSocket!.send(JSON.stringify(startRecordingMessage));

          // Start frame capture
          this.startFrameCapture();
        }
      };

      this.videoSocket.onclose = (event) => {
        this.updateStatus(
          `WebSocket closed (${event.code}: ${event.reason})`,
          "error",
          "ws"
        );
        this.stopStreaming();
      };

      this.videoSocket.onerror = () => {
        this.updateStatus("WebSocket error", "error", "ws");
      };

      this.videoSocket.onmessage = (event) => {
        if (typeof event.data === "string") {
          const message = JSON.parse(event.data);
          this.onMessage?.(message);
        } else {
          // Received video frame (for viewers)
          this.handleIncomingFrame(event.data);
        }
      };

      this.isStreaming = true;
      this.lastStatsUpdate = Date.now();
      this.updateStatus("Video streaming active", "success", "streaming");
    } catch (error) {
      this.onError?.(`Error starting video: ${error}`);
      this.stopStreaming();
      throw error;
    }
  }

  private startFrameCapture(): void {
    if (this.config.useH264) {
      this.startH264Recording();
      return;
    }

    const targetFrameTime = 1000 / this.config.frameRate; // ms per frame

    const captureFrame = () => {
      if (
        !this.isStreaming ||
        this.videoSocket?.readyState !== WebSocket.OPEN
      ) {
        return;
      }

      const now = Date.now();

      // Capture frame from video
      this.ctx!.drawImage(
        this.videoElement!,
        0,
        0,
        this.canvas!.width,
        this.canvas!.height
      );

      // Convert to JPEG blob
      this.canvas!.toBlob(
        (blob) => {
          if (blob && this.videoSocket?.readyState === WebSocket.OPEN) {
            // Convert blob to ArrayBuffer and send directly as binary
            blob.arrayBuffer().then((buffer) => {
              try {
                // Send raw binary data (backend expects large binary chunks as video)
                this.videoSocket!.send(buffer);

                this.framesSent++;
                this.totalBytes += buffer.byteLength;
                this.framesSentInLastSecond++;
                this.updateStats();
              } catch (error) {
                this.onError?.(`Error sending frame: ${error}`);
              }
            });
          }
        },
        "image/jpeg",
        this.config.quality
      );

      // Schedule next frame
      const elapsed = Date.now() - now;
      const nextFrameDelay = Math.max(0, targetFrameTime - elapsed);

      this.frameInterval = window.setTimeout(captureFrame, nextFrameDelay);
    };

    // Start capturing
    captureFrame();
  }

  private handleIncomingFrame(data: ArrayBuffer): void {
    try {
      // Parse metadata header
      const metadataLengthArray = new Uint32Array(data.slice(0, 4));
      const metadataLength = metadataLengthArray[0];

      const metadataBuffer = data.slice(4, 4 + metadataLength);
      const metadataString = new TextDecoder().decode(metadataBuffer);
      const metadata = JSON.parse(metadataString);

      const frameData = data.slice(4 + metadataLength);

      // Convert ArrayBuffer to Blob and create object URL
      const blob = new Blob([frameData], { type: "image/jpeg" });
      const imageUrl = URL.createObjectURL(blob);

      // Trigger message handler with frame data
      this.onMessage?.({
        type: "video-frame",
        metadata,
        imageUrl,
        frameSize: frameData.byteLength,
      });
    } catch (error) {
      this.onError?.(`Error processing incoming frame: ${error}`);
    }
  }

  stopStreaming(): void {
    this.isStreaming = false;

    if (this.frameInterval) {
      clearTimeout(this.frameInterval);
      this.frameInterval = null;
    }

    if (this.mediaRecorder && this.mediaRecorder.state !== "inactive") {
      this.mediaRecorder.stop();
      this.mediaRecorder = null;
    }

    if (this.videoStream) {
      this.videoStream.getTracks().forEach((track) => track.stop());
      this.videoStream = null;
    }

    if (this.videoSocket && this.videoSocket.readyState === WebSocket.OPEN) {
      // Send stop recording message
      const stopRecordingMessage = {
        type: "stop_recording",
      };
      this.videoSocket.send(JSON.stringify(stopRecordingMessage));

      this.videoSocket.close();
      this.videoSocket = null;
    }

    if (this.videoElement) {
      this.videoElement.srcObject = null;
    }

    this.updateStatus("Camera: Not accessed", "info", "camera");
    this.updateStatus("WebSocket: Not connected", "info", "ws");
    this.updateStatus("Video Streaming: Stopped", "info", "streaming");
  }

  private updateStatus(message: string, type: string, component: string): void {
    const stats = this.getStats();
    const status = {
      cameraStatus: component === "camera" ? message : "Camera: Not accessed",
      wsStatus: component === "ws" ? message : "WebSocket: Not connected",
      streamingStatus:
        component === "streaming" ? message : "Video Streaming: Stopped",
      stats,
    };
    this.onStatusChange?.(status);
  }

  private updateStats(): void {
    const now = Date.now();

    // Update FPS every second
    if (now - this.lastStatsUpdate >= 1000) {
      this.currentFPS = this.framesSentInLastSecond;
      this.framesSentInLastSecond = 0;
      this.lastStatsUpdate = now;
    }

    this.updateStatus("", "", "");
  }

  getStats(): VideoStats {
    const averageFrameSize =
      this.framesSent > 0 ? this.totalBytes / this.framesSent : 0;
    const compressionRatio = this.canvas
      ? (this.canvas.width * this.canvas.height * 3) /
        Math.max(averageFrameSize, 1)
      : 0;

    return {
      framesSent: this.framesSent,
      totalBytes: this.totalBytes,
      averageFrameSize: Math.round(averageFrameSize),
      currentFPS: this.currentFPS,
      compression: Math.round(compressionRatio * 100) / 100,
    };
  }

  // Update video quality in real-time
  updateQuality(quality: number): void {
    this.config.quality = Math.max(0.1, Math.min(1.0, quality));
  }

  // Update frame rate in real-time
  updateFrameRate(fps: number): void {
    this.config.frameRate = Math.max(1, Math.min(60, fps));
  }

  // Get current video track settings
  getVideoSettings() {
    if (this.videoStream) {
      const videoTrack = this.videoStream.getVideoTracks()[0];
      return videoTrack.getSettings();
    }
    return null;
  }

  private startH264Recording(): void {
    try {
      // Try H.264 first, fallback to other codecs
      const codecs = [
        "video/webm; codecs=h264",
        "video/webm; codecs=vp8",
        "video/mp4; codecs=h264",
      ];

      let selectedMimeType = "";
      for (const codec of codecs) {
        if (MediaRecorder.isTypeSupported(codec)) {
          selectedMimeType = codec;
          break;
        }
      }

      if (!selectedMimeType) {
        throw new Error("No supported video codec found");
      }

      this.mediaRecorder = new MediaRecorder(this.videoStream!, {
        mimeType: selectedMimeType,
        videoBitsPerSecond: this.config.quality * 1000000, // Convert quality to bitrate
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (
          event.data.size > 0 &&
          this.videoSocket?.readyState === WebSocket.OPEN
        ) {
          event.data.arrayBuffer().then((buffer) => {
            try {
              const metadata = {
                timestamp: Date.now(),
                width: this.canvas!.width,
                height: this.canvas!.height,
                codec: selectedMimeType.includes("h264") ? "h264" : "vp8",
                frameNumber: this.framesSent,
                mimeType: selectedMimeType,
              };

              const metadataString = JSON.stringify(metadata);
              const metadataBuffer = new TextEncoder().encode(metadataString);
              const metadataLength = new Uint32Array([metadataBuffer.length]);

              // Combine metadata length + metadata + video data
              const combined = new Uint8Array(
                4 + metadataBuffer.length + buffer.byteLength
              );
              combined.set(new Uint8Array(metadataLength.buffer), 0);
              combined.set(metadataBuffer, 4);
              combined.set(new Uint8Array(buffer), 4 + metadataBuffer.length);

              this.videoSocket!.send(combined.buffer);

              // Update stats
              this.framesSent++;
              this.totalBytes += combined.byteLength;
              this.updateStats();
            } catch (error) {
              this.onError?.(`Error sending H.264 data: ${error}`);
            }
          });
        }
      };

      this.mediaRecorder.onerror = (event) => {
        this.onError?.(`MediaRecorder error: ${event}`);
      };

      // Start recording with regular chunks
      const chunkInterval = 1000 / this.config.frameRate; // Send data based on frame rate
      this.mediaRecorder.start(chunkInterval);
      this.isStreaming = true;
      this.updateStatus(
        `${
          selectedMimeType.includes("h264") ? "H.264" : "VP8"
        } streaming active`,
        "success",
        "stream"
      );
    } catch (error) {
      this.onError?.(`Failed to start H.264 recording: ${error}`);
      // Fallback to JPEG
      this.config.useH264 = false;
      this.startFrameCapture();
    }
  }
}
