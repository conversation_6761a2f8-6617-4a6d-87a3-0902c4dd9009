import {
  AuthResponse,
  <PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  SpeakerLoginRequest,
  SpeakerLoginResponse,
  RoomCreateRequest,
  RoomJoinRequest,
  RoomLeaveRequest,
  RoomChatRequest,
  UserProfile,
  MeetingCreate,
  MeetingUpdate,
  MeetingLanguageCreate,
  MeetingJoin,
  Meeting,
  MeetingLanguage,
  ChatMessageInc,
} from "./types";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001";

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      credentials: "include", // Include cookies for session management
      ...options,
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Authentication endpoints
  async register(data: UserRegister): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/register", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async login(data: UserLogin): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/login", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async guestJoin(data: GuestJoin): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/guest", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async logout(): Promise<void> {
    return this.request<void>("/auth/logout", {
      method: "POST",
    });
  }

  async getCurrentUser(): Promise<UserProfile> {
    return this.request<UserProfile>("/auth/me");
  }

  async getAuthStatus(): Promise<any> {
    return this.request<any>("/auth/status");
  }

  // Speaker authentication (separate endpoint)
  async speakerLogin(data: SpeakerLoginRequest): Promise<SpeakerLoginResponse> {
    return this.request<SpeakerLoginResponse>("/meetings/auth/speaker_login", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Meeting room endpoints
  async createRoom(data: RoomCreateRequest): Promise<any> {
    return this.request<any>("/meetings/rooms/create", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async joinRoom(data: RoomJoinRequest): Promise<any> {
    return this.request<any>("/meetings/rooms/join_room", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async leaveRoom(data: RoomLeaveRequest): Promise<any> {
    return this.request<any>("/meetings/rooms/leave_room", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getRoomMetadata(roomId: number): Promise<any> {
    return this.request<any>(`/meetings/rooms/metadata/${roomId}`);
  }

  async listRooms(): Promise<any> {
    return this.request<any>("/meetings/rooms/list_rooms");
  }

  // Chat endpoints
  async getRoomChat(): Promise<any> {
    return this.request<any>("/meetings/rooms/chat");
  }

  async postRoomChat(data: RoomChatRequest): Promise<any> {
    return this.request<any>("/meetings/rooms/chat", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // New Meeting Management Endpoints (aligned with backend)
  async createMeeting(data: MeetingCreate): Promise<Meeting> {
    return this.request<Meeting>("/meetings/", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getMyMeetings(): Promise<Meeting[]> {
    return this.request<Meeting[]>("/meetings/my-meetings");
  }

  async getMeeting(id: number): Promise<Meeting> {
    return this.request<Meeting>(`/meetings/${id}`);
  }

  async updateMeeting(id: number, data: MeetingUpdate): Promise<Meeting> {
    return this.request<Meeting>(`/meetings/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async addLanguageToMeeting(
    meetingId: number,
    data: MeetingLanguageCreate
  ): Promise<MeetingLanguage> {
    return this.request<MeetingLanguage>(`/meetings/${meetingId}/languages`, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getMeetingLanguages(meetingId: number): Promise<MeetingLanguage[]> {
    return this.request<MeetingLanguage[]>(`/meetings/${meetingId}/languages`);
  }

  async joinMeeting(data: MeetingJoin): Promise<any> {
    return this.request<any>("/meetings/join", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getMeetingByCode(code: string): Promise<Meeting> {
    return this.request<Meeting>(`/meetings/code/${code}`);
  }

  // Chat endpoint (aligned with backend)
  async postChatMessage(data: ChatMessageInc): Promise<any> {
    return this.request<any>("/chat", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.request<any>("/health");
  }
}

export const apiClient = new ApiClient();
export default apiClient;
