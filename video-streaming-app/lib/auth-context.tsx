"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { UserProfile } from "./types";

interface AuthContextType {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string, user: UserProfile) => void;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  const login = (token: string, userData: UserProfile) => {
    // Store session token for API requests (backend uses cookies but we'll keep token for compatibility)
    localStorage.setItem("session_token", token);
    localStorage.setItem("user_data", JSON.stringify(userData));
    setUser(userData);
  };

  const logout = async () => {
    try {
      // Call backend logout endpoint to clear session
      await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001"
        }/auth/logout`,
        {
          method: "POST",
          credentials: "include", // Include cookies
        }
      );
    } catch (error) {
      console.error("Logout error:", error);
    }

    // Clear local storage
    localStorage.removeItem("session_token");
    localStorage.removeItem("user_data");
    setUser(null);
    router.push("/");
  };

  const checkAuth = async (): Promise<boolean> => {
    try {
      // Check with backend auth status endpoint
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001"
        }/auth/status`,
        {
          method: "GET",
          credentials: "include", // Include cookies for session
        }
      );

      if (response.ok) {
        const authData = await response.json();
        if (authData.authenticated && authData.user) {
          setUser(authData.user);
          setIsLoading(false);
          return true;
        }
      }

      // Fallback to local storage check
      const userData = localStorage.getItem("user_data");
      if (userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsLoading(false);
          return true;
        } catch {
          // Invalid user data, clear storage
          localStorage.removeItem("session_token");
          localStorage.removeItem("user_data");
        }
      }

      setIsLoading(false);
      return false;
    } catch (error) {
      console.error("Auth check failed:", error);

      // Fallback to local storage check
      const userData = localStorage.getItem("user_data");
      if (userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsLoading(false);
          return true;
        } catch {
          localStorage.removeItem("session_token");
          localStorage.removeItem("user_data");
        }
      }

      setIsLoading(false);
      return false;
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
