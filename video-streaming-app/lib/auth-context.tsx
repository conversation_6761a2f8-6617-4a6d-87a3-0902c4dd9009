'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { UserProfile } from './types';

interface AuthContextType {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string, user: UserProfile) => void;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  const login = (token: string, userData: UserProfile) => {
    localStorage.setItem('speaker_token', token);
    localStorage.setItem('user_data', JSON.stringify(userData));
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('speaker_token');
    localStorage.removeItem('user_data');
    setUser(null);
    router.push('/');
  };

  const checkAuth = async (): Promise<boolean> => {
    try {
      const token = localStorage.getItem('speaker_token');
      const userData = localStorage.getItem('user_data');
      
      if (!token || !userData) {
        setIsLoading(false);
        return false;
      }

      // In a real app, you would validate the token with the server
      // For now, we'll just check if the token exists and is not expired
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsLoading(false);
        return true;
      } catch {
        // Invalid user data, clear storage
        localStorage.removeItem('speaker_token');
        localStorage.removeItem('user_data');
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsLoading(false);
      return false;
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
