// API Types based on backend-docs.json

export interface UserProfile {
  id: number;
  email: string;
  full_name: string;
  preferred_language: string;
  original_language: string;
  voice_sample_url?: string;
  created_at: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: UserProfile;
  session_token?: string;
}

export interface UserLogin {
  email: string;
  password: string;
}

export interface UserRegister {
  email: string;
  password: string;
  full_name: string;
  preferred_language?: string;
  original_language?: string;
}

export interface GuestJoin {
  guest_name?: string;
}

// Meeting Management Types
export interface MeetingCreate {
  title: string;
  description?: string;
  host_language?: string;
  is_public?: boolean;
}

export interface MeetingUpdate {
  title?: string;
  description?: string;
  status?: string;
}

export interface MeetingLanguageCreate {
  language_code: string;
  language_name: string;
}

export interface MeetingJoin {
  meeting_code: string;
  display_name: string;
  selected_language?: string;
}

export interface Meeting {
  id: number;
  host_id: number;
  title: string;
  description?: string;
  host_language: string;
  meeting_code: string;
  status: string;
  is_public: boolean;
  created_at: string;
  started_at?: string;
  ended_at?: string;
  max_languages: number;
  lang_limit?: number;
}

export interface MeetingLanguage {
  id: number;
  meeting_id: number;
  language_code: string;
  language_name: string;
  stream_url?: string;
  cloudflare_video_uid?: string;
  status: string;
  created_at: string;
}

// Legacy types for compatibility
export interface SpeakerLoginRequest {
  email: string;
  password: string;
}

export interface SpeakerLoginResponse {
  access_token: string;
  token_type?: string;
  error?: string;
}

export interface RoomCreateRequest {
  title: string;
  description?: string;
  is_public?: boolean;
  host_language?: string;
  jwt: string;
}

export interface RoomJoinRequest {
  room_id: number;
  viewer_id: string;
  viewer_name?: string;
}

export interface RoomLeaveRequest {
  room_id: number;
  viewer_id: string;
}

export interface RoomChatRequest {
  room_id: number;
  jwt?: string;
  viewer_id?: string;
  viewer_name?: string;
  text: string;
}

export interface ChatMessageInc {
  text: string;
  room_id: string;
}

export interface ChatMessage {
  id: string;
  user: string;
  message: string;
  time: string;
  avatar?: string;
}

export interface Participant {
  id: string;
  name: string;
  role: "Host" | "Participant";
  isMuted: boolean;
  isVideoOn: boolean;
  avatar?: string;
}

// Audio streaming types
export interface AudioConfig {
  sampleRate: number;
  channelCount: number;
  encoding: "pcm16";
}

export interface WebSocketMessage {
  type: "join-room" | "leave-room" | "audio-data" | "chat" | "error";
  roomId?: string;
  role?: "preacher" | "listener";
  audioConfig?: AudioConfig;
  data?: any;
  message?: string;
}

// Meeting room state
export interface MeetingRoom {
  id: number;
  title: string;
  description?: string;
  is_public: boolean;
  host_language: string;
  created_at: string;
  participants: Participant[];
  chat_messages: ChatMessage[];
}

// User role types
export type UserRole = "speaker" | "guest";

// Language options
export const SUPPORTED_LANGUAGES = [
  { code: "en-US", name: "English (US)" },
  { code: "uk-UA", name: "Ukrainian" },
  { code: "ru-RU", name: "Russian" },
  { code: "pl-PL", name: "Polish" },
  { code: "de-DE", name: "German" },
  { code: "fr-FR", name: "French" },
  { code: "es-ES", name: "Spanish" },
  { code: "zh-CN", name: "Chinese" },
  { code: "auto", name: "Auto Detect" },
] as const;

export type LanguageCode = (typeof SUPPORTED_LANGUAGES)[number]["code"];
