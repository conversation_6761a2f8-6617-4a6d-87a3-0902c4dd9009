"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Languages, Plus, Trash2, Globe } from "lucide-react";
import { useAddLanguageToMeeting } from "@/lib/hooks";
import { MeetingLanguage, SUPPORTED_LANGUAGES } from "@/lib/types";

interface LanguageManagerProps {
  meetingId: number;
  currentLanguages: MeetingLanguage[];
  maxLanguages?: number;
}

export function LanguageManager({ 
  meetingId, 
  currentLanguages = [], 
  maxLanguages = 5 
}: LanguageManagerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguageCode, setSelectedLanguageCode] = useState("");
  
  const addLanguageMutation = useAddLanguageToMeeting();

  const availableLanguages = SUPPORTED_LANGUAGES.filter(
    (lang) => !currentLanguages.some((current) => current.language_code === lang.code)
  );

  const canAddMore = currentLanguages.length < maxLanguages;

  const handleAddLanguage = async () => {
    if (!selectedLanguageCode) return;

    const selectedLang = SUPPORTED_LANGUAGES.find(
      (lang) => lang.code === selectedLanguageCode
    );
    
    if (!selectedLang) return;

    try {
      await addLanguageMutation.mutateAsync({
        meetingId,
        data: {
          language_code: selectedLang.code,
          language_name: selectedLang.name,
        },
      });
      
      setSelectedLanguageCode("");
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to add language:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ready":
        return "bg-green-100 text-green-800";
      case "streaming":
        return "bg-blue-100 text-blue-800";
      case "error":
        return "bg-red-100 text-red-800";
      default:
        return "bg-yellow-100 text-yellow-800";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Globe className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold">Languages</h3>
          <Badge variant="secondary">
            {currentLanguages.length}/{maxLanguages}
          </Badge>
        </div>
        
        {canAddMore && (
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="gap-2">
                <Plus className="w-4 h-4" />
                Add Language
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Translation Language</DialogTitle>
                <DialogDescription>
                  Add a new language for real-time translation. Maximum {maxLanguages} languages per meeting.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Select Language</label>
                  <Select value={selectedLanguageCode} onValueChange={setSelectedLanguageCode}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a language..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableLanguages.map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleAddLanguage}
                  disabled={!selectedLanguageCode || addLanguageMutation.isPending}
                >
                  {addLanguageMutation.isPending ? "Adding..." : "Add Language"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <div className="grid gap-3">
        {currentLanguages.map((language) => (
          <div
            key={language.id}
            className="flex items-center justify-between p-3 border rounded-lg bg-white"
          >
            <div className="flex items-center gap-3">
              <Languages className="w-4 h-4 text-gray-500" />
              <div>
                <p className="font-medium">{language.language_name}</p>
                <p className="text-sm text-gray-500">{language.language_code}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(language.status)}>
                {language.status}
              </Badge>
              
              {language.stream_url && (
                <Badge variant="outline" className="text-xs">
                  Stream Ready
                </Badge>
              )}
            </div>
          </div>
        ))}
        
        {currentLanguages.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Languages className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>No additional languages added yet</p>
            <p className="text-sm">Add languages for real-time translation</p>
          </div>
        )}
      </div>
    </div>
  );
}
