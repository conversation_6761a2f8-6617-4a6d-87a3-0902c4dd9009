"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { X } from "lucide-react";
import { Button } from "./button";

interface ModalProps {
  children: React.ReactNode;
  className?: string;
}

export function Modal({ children, className = "" }: ModalProps) {
  const router = useRouter();

  // Handle ESC key press
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        router.back();
      }
    };

    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, [router]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      router.back();
    }
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <div
      className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div className={`relative max-w-md w-full ${className}`}>
        {/* Close button - positioned absolute to appear outside the modal content */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="absolute -top-2 -right-2 z-10 text-white hover:bg-white/20 rounded-full w-8 h-8 p-0 bg-black/30"
        >
          <X className="w-4 h-4" />
        </Button>
        {children}
      </div>
    </div>
  );
}
