"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  MessageSquare,
  Send,
  Languages,
  Volume2,
  X,
  Settings,
  Globe,
  PhoneOff,
} from "lucide-react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import {
  useGuestJoin,
  useMeetingByCode,
  useJoinMeeting,
  useMeetingLanguages,
} from "@/lib/hooks";
import { SUPPORTED_LANGUAGES } from "@/lib/types";
import { AudioStreamingClient } from "@/lib/audio-streaming";

export default function GuestJoinPage() {
  const [guestName, setGuestName] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("en-US");
  const [isJoined, setIsJoined] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(false);
  const [chatMessage, setChatMessage] = useState("");
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [audioClient, setAudioClient] = useState<AudioStreamingClient | null>(
    null
  );
  const [audioStatus, setAudioStatus] = useState<any>({});

  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const meetingCode = params.code as string;

  const guestJoinMutation = useGuestJoin();
  const joinMeetingMutation = useJoinMeeting();
  const {
    data: meetingData,
    isLoading: meetingLoading,
    error: meetingError,
  } = useMeetingByCode(meetingCode);
  const { data: meetingLanguages } = useMeetingLanguages(meetingData?.id || 0);

  // Check if user joined from modal
  const joinedFromModal = searchParams.get("joined") === "true";
  const nameFromModal = searchParams.get("name");
  const langFromModal = searchParams.get("lang");

  useEffect(() => {
    // Initialize audio client
    const client = new AudioStreamingClient(
      setAudioStatus,
      (message) => {
        if (message.type === "chat") {
          setChatMessages((prev) => [...prev, message]);
        }
      },
      (error) => console.error("Audio error:", error)
    );
    setAudioClient(client);

    return () => {
      client.stopStreaming();
    };
  }, []);

  useEffect(() => {
    // If user joined from modal, auto-join the meeting
    if (joinedFromModal && nameFromModal && langFromModal) {
      setGuestName(nameFromModal);
      setSelectedLanguage(langFromModal);
      setIsJoined(true);

      // Start audio streaming for receiving translated audio
      if (audioClient) {
        audioClient.startStreaming(
          "auto", // Auto-detect source language
          langFromModal as any,
          meetingCode
        );
      }
    }
  }, [joinedFromModal, nameFromModal, langFromModal, audioClient, meetingCode]);

  const handleJoinMeeting = async () => {
    if (!guestName.trim()) {
      alert("Please enter your name");
      return;
    }

    if (!meetingData) {
      alert("Meeting not found. Please check the meeting code.");
      return;
    }

    try {
      // First authenticate as guest
      await guestJoinMutation.mutateAsync({
        guest_name: guestName,
      });

      // Join the meeting using the new backend API
      await joinMeetingMutation.mutateAsync({
        meeting_code: meetingCode,
        display_name: guestName,
        selected_language: selectedLanguage,
      });

      // Redirect to the meeting room with guest parameters
      router.push(
        `/meeting/${meetingData.id}?guest=true&name=${encodeURIComponent(
          guestName
        )}&lang=${selectedLanguage}&code=${meetingCode}`
      );
    } catch (error) {
      console.error("Failed to join meeting:", error);
      alert("Failed to join meeting. Please check the meeting code.");
      router.push("/");
    }
  };

  const toggleMic = async () => {
    setIsMicOn(!isMicOn);
    // In a real implementation, this would control the microphone
  };

  const toggleVideo = async () => {
    setIsVideoOn(!isVideoOn);
    // In a real implementation, this would control the camera
  };

  const sendMessage = () => {
    if (chatMessage.trim()) {
      const newMessage = {
        id: Date.now(),
        user: guestName || "Guest",
        message: chatMessage,
        time: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };
      setChatMessages([...chatMessages, newMessage]);
      setChatMessage("");
    }
  };

  const handleLeave = () => {
    if (audioClient) {
      audioClient.stopStreaming();
    }
    router.push("/");
  };

  const handleClose = () => {
    router.push("/");
  };

  // Show loading state while fetching meeting data
  if (meetingLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-zinc-600">Loading meeting...</p>
        </div>
      </div>
    );
  }

  // Show error state if meeting not found
  if (meetingError || !meetingData) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            Meeting Not Found
          </h1>
          <p className="text-zinc-600 mb-4">
            The meeting code "{meetingCode}" is invalid or the meeting has
            ended.
          </p>
          <Button
            onClick={() => router.push("/")}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  if (!isJoined) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="bg-[#F3F4F7] rounded-2xl w-full max-w-md relative overflow-hidden">
          {/* Header */}
          <div className="bg-[#18191B] px-8 h-32 pb-4 flex items-end relative rounded-t-2xl">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="absolute top-4 right-4 text-[#F3F4F7] hover:bg-[rgba(102,119,153,0.1)] rounded-full w-8 h-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>

            {/* Header */}
            <div className="text-left">
              <h1 className="text-[#F3F4F7] text-[28px] font-[590] leading-[1.193] tracking-[-0.03em]">
                Join Meeting
              </h1>
            </div>
          </div>

          {/* Form Content */}
          <div className="px-8 py-8">
            {/* Meeting Code Display */}
            <div className="mb-6 text-center">
              <p className="text-[#525F7A] text-[16px] font-[400] leading-[1.193] tracking-[-0.03em]">
                Meeting Code:{" "}
                <span className="font-[590] text-[#0E1115]">{meetingCode}</span>
              </p>
            </div>

            <div className="space-y-6">
              {/* Name Input */}
              <div>
                <Input
                  placeholder="Enter your name ..."
                  value={guestName}
                  onChange={(e) => setGuestName(e.target.value)}
                  className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 text-[20px] font-[400] leading-[1.193] tracking-[-0.03em] focus:ring-2 focus:ring-[#2060DF]"
                />
              </div>

              {/* Language Selection */}
              <div>
                <Select
                  value={selectedLanguage}
                  onValueChange={setSelectedLanguage}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select language..." />
                  </SelectTrigger>
                  <SelectContent>
                    {meetingLanguages && meetingLanguages.length > 0
                      ? meetingLanguages.map((lang) => (
                          <SelectItem
                            key={lang.language_code}
                            value={lang.language_code}
                          >
                            {lang.language_name}
                          </SelectItem>
                        ))
                      : SUPPORTED_LANGUAGES.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.name}
                          </SelectItem>
                        ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Join Button */}
              <Button
                onClick={handleJoinMeeting}
                disabled={guestJoinMutation.isPending}
                className="w-full bg-[#2060DF] hover:bg-[#2060DF]/90 text-[#F3F4F7] rounded-full py-3 text-[20px] font-[590] leading-[1em] shadow-lg"
              >
                {guestJoinMutation.isPending ? "Joining..." : "Join Meeting"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-[#F3F4F7] flex flex-col">
      {/* Header */}
      <header className="flex items-center justify-between gap-[995px] px-4 py-4 h-[88px]">
        <div className="flex items-center gap-4">
          {/* Logo Group */}
          <div className="flex items-center gap-[19.2px]">
            {/* Main Logo */}
            <div className="w-16 h-16 bg-[#18191B] rounded-[12.8px] flex items-center justify-center">
              <svg
                width="64"
                height="64"
                viewBox="0 0 64 64"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect width="64" height="64" rx="12.8" fill="#18191B" />
                <path
                  d="M39.1503 24.4211C40.9371 22.9747 43.8116 16.3123 45.1828 13.2597C46.5539 10.2071 49.4952 7.88945 55.5327 9.90578C50.1084 28.8044 58.0614 33.1455 49.3779 42.6848C44.0002 48.5926 33.0103 46.595 23.8745 44.5773C23.5695 45.5374 23.3008 46.6065 23.0782 47.7989C20.6811 60.639 8.18839 49.1212 9.19777 44.1459C9.80259 41.1648 16.1811 42.8782 23.8745 44.5773C26.854 35.1977 33.3005 36.2178 34.1162 34.3579C21.5909 39.3639 28.897 22.5951 11.5776 21.0112C15.0166 10.1763 26.1885 7.03555 32.7634 10.3375C39.3383 13.6395 36.0696 26.915 39.1503 24.4211Z"
                  fill="white"
                  stroke="white"
                  stroke-width="3.2"
                />
                <circle cx="50.8389" cy="12.7308" r="2.92219" fill="black" />
              </svg>
            </div>

            {/* Logos Live Text */}
            <div className="text-[#0E1115] text-[32px] font-[590] leading-[1.193] tracking-[-0.03em]">
              Logos Live
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Language Indicator */}
          <div className="flex items-center gap-2 bg-[#E0E4EB] rounded-full px-3 py-1">
            <Languages className="w-4 h-4 text-[#2060DF]" />
            <span className="text-[#0E1115] text-sm font-[590]">
              {selectedLanguage}
            </span>
          </div>

          {/* Leave Button */}
          <Button
            onClick={handleLeave}
            className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white rounded-full px-4 py-2 h-10 font-[590]"
          >
            <PhoneOff className="w-4 h-4 mr-2" />
            Leave
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Video Area */}
        <div className="flex-1 relative bg-[#18191B] rounded-2xl mx-4 mb-4">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <Avatar className="w-32 h-32 mx-auto mb-4">
                <AvatarImage src="/placeholder-speaker.jpg" />
                <AvatarFallback className="text-4xl bg-[#525F7A] text-white">
                  SP
                </AvatarFallback>
              </Avatar>
              <h2 className="text-white text-xl font-[590] mb-2">Speaker</h2>
              <p className="text-[#94A3B8]">Listening to translated audio...</p>

              {/* Audio Status */}
              <div className="mt-4 text-sm text-[#94A3B8]">
                <div className="flex items-center justify-center gap-2">
                  <Volume2 className="w-4 h-4" />
                  <span>
                    Audio: {audioStatus.audioStatus || "Connecting..."}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
            <div className="flex items-center gap-4">
              <Button
                onClick={toggleMic}
                className={`rounded-full w-12 h-12 p-0 ${
                  isMicOn
                    ? "bg-[#22C55E] hover:bg-[#22C55E]/90"
                    : "bg-[#525F7A] hover:bg-[#525F7A]/90"
                }`}
              >
                {isMicOn ? (
                  <Mic className="w-5 h-5" />
                ) : (
                  <MicOff className="w-5 h-5" />
                )}
              </Button>

              <Button
                onClick={toggleVideo}
                className={`rounded-full w-12 h-12 p-0 ${
                  isVideoOn
                    ? "bg-[#22C55E] hover:bg-[#22C55E]/90"
                    : "bg-[#525F7A] hover:bg-[#525F7A]/90"
                }`}
              >
                {isVideoOn ? (
                  <Video className="w-5 h-5" />
                ) : (
                  <VideoOff className="w-5 h-5" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Chat Sidebar */}
        <div className="w-80 bg-white border-l border-[#E0E4EB] flex flex-col mr-4 mb-4 rounded-2xl">
          <div className="p-4 border-b border-[#E0E4EB]">
            <h3 className="font-[590] text-[#0E1115] flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Chat
            </h3>
          </div>

          <div className="flex-1 p-4 overflow-y-auto space-y-4">
            {chatMessages.map((msg) => (
              <div key={msg.id} className="flex gap-3">
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarFallback className="text-xs bg-[#E0E4EB] text-[#0E1115]">
                    {msg.user
                      .split(" ")
                      .map((n: string) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-[590] text-[#0E1115] truncate">
                      {msg.user}
                    </span>
                    <span className="text-xs text-[#525F7A]">{msg.time}</span>
                  </div>
                  <p className="text-sm text-[#525F7A] break-words">
                    {msg.message}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="p-4 border-t border-[#E0E4EB]">
            <div className="flex gap-2">
              <Input
                placeholder="Type a message..."
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && sendMessage()}
                className="flex-1 bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-4 py-2 text-[16px] font-[400] h-[40px]"
              />
              <Button
                onClick={sendMessage}
                className="bg-[#2060DF] hover:bg-[#2060DF]/90 text-white rounded-full px-3 h-[40px]"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
