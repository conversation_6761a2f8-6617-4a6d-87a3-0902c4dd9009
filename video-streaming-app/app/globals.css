@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: #f3f4f7;
  --foreground: #0e1115;
  --card: #ffffff;
  --card-foreground: #0e1115;
  --popover: #ffffff;
  --popover-foreground: #0e1115;
  --primary: #2060df;
  --primary-foreground: #f3f4f7;
  --secondary: #e0e4eb;
  --secondary-foreground: #0e1115;
  --muted: #e0e4eb;
  --muted-foreground: #525f7a;
  --accent: #e0e4eb;
  --accent-foreground: #0e1115;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e0e4eb;
  --input: #e0e4eb;
  --ring: #2060df;
  --chart-1: #e0e4eb;
  --chart-2: #f3f4f7;
  --chart-3: #525f7a;
  --chart-4: #2060df;
  --chart-5: #18191b;
  --radius: 0.5rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #0e1115;
  --sidebar-primary: #f3f4f7;
  --sidebar-primary-foreground: #0e1115;
  --sidebar-accent: #2060df;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #e0e4eb;
  --sidebar-ring: #2060df;
}

.dark {
  --background: #18191b;
  --foreground: #f3f4f7;
  --card: #1e293b;
  --card-foreground: #f3f4f7;
  --popover: #1e293b;
  --popover-foreground: #f3f4f7;
  --primary: #2060df;
  --primary-foreground: #f3f4f7;
  --secondary: #525f7a;
  --secondary-foreground: #f3f4f7;
  --muted: #525f7a;
  --muted-foreground: #94a3b8;
  --accent: #525f7a;
  --accent-foreground: #f3f4f7;
  --destructive: #dc2626;
  --destructive-foreground: #f3f4f7;
  --border: #525f7a;
  --input: #525f7a;
  --ring: #2060df;
  --chart-1: #1e293b;
  --chart-2: #525f7a;
  --chart-3: #94a3b8;
  --chart-4: #2060df;
  --chart-5: #f3f4f7;
  --sidebar: #1e293b;
  --sidebar-foreground: #f3f4f7;
  --sidebar-primary: #2060df;
  --sidebar-primary-foreground: #f3f4f7;
  --sidebar-accent: #525f7a;
  --sidebar-accent-foreground: #f3f4f7;
  --sidebar-border: #525f7a;
  --sidebar-ring: #2060df;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
