"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ChevronLeft,
  ChevronRight,
  Settings,
  Zap,
  Globe,
  LogOut,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { useCreateRoom, useCreateMeeting } from "@/lib/hooks";

export default function HomePage() {
  const [meetingCode, setMeetingCode] = useState("");
  const router = useRouter();
  const { isAuthenticated, isLoading, user, logout } = useAuth();
  const createRoomMutation = useCreateRoom();
  const createMeetingMutation = useCreateMeeting();

  const handleNewMeet = async () => {
    if (isAuthenticated) {
      // User is authenticated, create a new meeting using the new backend API
      try {
        const result = await createMeetingMutation.mutateAsync({
          title: `Meeting - ${new Date().toLocaleString()}`,
          description: "New meeting room",
          is_public: true,
          host_language: "en",
        });

        if (result.id) {
          // Start the meeting and redirect to meeting page
          router.push(`/meeting/${result.id}`);
        }
      } catch (error) {
        console.error("Failed to create meeting:", error);
        alert("Failed to create meeting. Please try again.");
      }
    } else {
      // User is not authenticated, show login modal
      router.push("/auth/login");
    }
  };

  const handleLogout = () => {
    logout();
  };

  const handleJoinConference = () => {
    if (meetingCode.trim()) {
      router.push(`/join/${meetingCode}`);
    }
  };

  const features = [
    {
      icon: "🎥",
      title: "Help note",
      description:
        "Crystal clear HD video, spatial audio and intelligent noise reduction. All this works right in your browser without additional installations.",
    },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-zinc-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-zinc-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F3F4F7] w-full relative">
      <div className="min-h-screen mx-auto relative flex flex-col">
        {/* Header */}
        <header className="absolute top-0 max-w-[1280px] mx-auto left-0 right-0 z-10 flex items-center justify-between px-4 py-4 h-[88px]">
          <div className="flex items-center gap-4">
            {/* Logo Group */}
            <div className="flex items-center gap-[19.2px]">
              {/* Main Logo */}
              <div className="w-16 h-16 bg-[#18191B] rounded-[12.8px] flex items-center justify-center">
                <svg
                  width="64"
                  height="64"
                  viewBox="0 0 64 64"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect width="64" height="64" rx="12.8" fill="#18191B" />
                  <path
                    d="M39.1503 24.4211C40.9371 22.9747 43.8116 16.3123 45.1828 13.2597C46.5539 10.2071 49.4952 7.88945 55.5327 9.90578C50.1084 28.8044 58.0614 33.1455 49.3779 42.6848C44.0002 48.5926 33.0103 46.595 23.8745 44.5773C23.5695 45.5374 23.3008 46.6065 23.0782 47.7989C20.6811 60.639 8.18839 49.1212 9.19777 44.1459C9.80259 41.1648 16.1811 42.8782 23.8745 44.5773C26.854 35.1977 33.3005 36.2178 34.1162 34.3579C21.5909 39.3639 28.897 22.5951 11.5776 21.0112C15.0166 10.1763 26.1885 7.03555 32.7634 10.3375C39.3383 13.6395 36.0696 26.915 39.1503 24.4211Z"
                    fill="white"
                    stroke="white"
                    strokeWidth="3.2"
                  />
                  <circle cx="50.8389" cy="12.7308" r="2.92219" fill="black" />
                </svg>
              </div>
              {/* Logo Text */}
              <span className="text-[#0E1115] text-[24px] whitespace-nowrap font-[590] leading-[1.193] tracking-[-0.02em]">
                Logos Live
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {isAuthenticated ? (
              <>
                {/* Settings Button */}
                {/* <Button
                  variant="ghost"
                  size="sm"
                  className="rounded-full w-10 h-10 p-2 flex items-center justify-center"
                >
                  <Settings className="w-6 h-6 text-[#18191B]" />
                </Button>

                <div className="flex items-center justify-center gap-2 bg-[rgba(102,119,153,0.1)] rounded-full px-5 py-0 h-10 shadow-[4px_4px_48px_0px_rgba(255,255,255,0.3)]">
                  <Zap className="w-6 h-6 text-[#0F1116]" />
                  <span className="text-[#0F1116] text-[18px] font-[590] leading-[1em]">
                    0
                  </span>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  className="rounded-full w-10 h-10 p-2 bg-[rgba(102,119,153,0.1)] shadow-[4px_4px_48px_0px_rgba(255,255,255,0.3)] flex items-center justify-center"
                >
                  <Globe className="w-6 h-6 text-[#0F1116]" />
                </Button> */}

                {/* Avatar with Logout */}
                <div className="relative flex items-center gap-2">
                  {/* <Avatar className="w-12 h-12">
                    <AvatarImage src="/placeholder-user.jpg" />
                    <AvatarFallback className="bg-[#18191B] text-[#F3F4F7] text-[16px] font-[590] leading-[1em]">
                      {user?.full_name
                        ?.split(" ")
                        .map((n) => n[0])
                        .join("") || "U"}
                    </AvatarFallback>
                  </Avatar> */}
                  {/* Badge */}
                  {/* <div className="absolute top-[33.61px] left-[34px] w-[10px] h-[10px] bg-[#2060DF] rounded-full"></div> */}

                  {/* Logout Button */}
                  <Button
                    onClick={handleLogout}
                    variant="ghost"
                    size="sm"
                    className="rounded-full w-12 h-12 p-2 flex items-center justify-center hover:bg-red-100"
                    title="Logout"
                  >
                    <LogOut className="w-5 h-5 text-[#DC2626]" />
                  </Button>
                </div>
              </>
            ) : (
              <>
                {/* Login Button for non-authenticated users */}
                <Button
                  onClick={() => router.push("/auth/login")}
                  className="bg-[#2060DF] hover:bg-[#2060DF]/90 text-white rounded-full px-4 py-2 h-10 font-[590]"
                >
                  Login
                </Button>
                <Button
                  onClick={() => router.push("/auth/register")}
                  variant="outline"
                  className="border-2 border-[#2060DF] text-[#2060DF] hover:bg-[#2060DF] hover:text-white rounded-full px-4 py-2 h-10 font-[590] bg-transparent"
                >
                  Sign Up
                </Button>
              </>
            )}
          </div>
        </header>

        {/* Main Content */}
        <main className="flex flex-grow max-w-[1280px] mx-auto flex-1 flex-col items-center pt-[100px] pb-0 px-0 gap-20 h-full">
          <div className="flex flex-col items-center gap-20">
            {/* Text Section */}
            <div className="flex flex-col items-center gap-6">
              <div className="flex flex-col items-center w-[540px]">
                <h1 className="text-[#0E1115] text-[64px] font-[510] leading-[1.193] tracking-[-0.03em] text-center w-full">
                  VideoMeet
                </h1>
                <p className="text-[#0E1115] text-[32px] font-[510] leading-[1.193] tracking-[-0.03em] text-center w-full">
                  New Generation Meetings
                  <br />
                  Simple. Reliable. Beautiful
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-row items-center gap-2">
              <Button
                onClick={handleNewMeet}
                disabled={createRoomMutation.isPending}
                className="bg-[#18191B] hover:bg-[#18191B]/90 text-[#F3F4F7] px-6 py-3 rounded-full text-[20px] font-[590] leading-[1em] h-[50px] flex items-center justify-center gap-2"
              >
                {createRoomMutation.isPending
                  ? "Creating..."
                  : isAuthenticated
                  ? "New Meet"
                  : "Get Started"}
              </Button>
              <Button
                onClick={handleJoinConference}
                variant="outline"
                className="border-2 border-[#2060DF] text-[#2060DF] hover:bg-[#2060DF] hover:text-white px-6 py-3 rounded-full text-[20px] font-[590] leading-[1em] h-[50px] flex items-center justify-center gap-2 bg-transparent"
              >
                Join Conference
              </Button>
              <div className="w-60">
                <Input
                  placeholder="Enter Meeting Code ..."
                  value={meetingCode}
                  onChange={(e) => setMeetingCode(e.target.value)}
                  className="rounded-[20px] px-5 py-3 text-[20px] bg-[#E0E4EB] border-0 text-[#525F7A] placeholder-[#525F7A] font-[400] leading-[1.193] tracking-[-0.03em] h-[50px]"
                  onKeyDown={(e) => e.key === "Enter" && handleJoinConference()}
                />
              </div>
            </div>
          </div>

          {/* Helper Section */}
          <div className="flex flex-col items-center gap-4 relative w-full max-w-[500px]">
            {/* Navigation */}
            <div className="flex items-center justify-between w-full gap-14 relative h-[180px]">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full w-10 h-10 p-2 flex items-center justify-center absolute left-0 top-1/2 -translate-y-1/2 z-10"
              >
                <ChevronLeft className="w-6 h-6 text-[#3E485C]" />
              </Button>

              <div className="bg-[#2060DF] rounded-[40px] w-[180px] h-[180px] absolute left-1/2 -translate-x-1/2 flex items-center justify-center">
                <img
                  src="/camera.png"
                  alt="Camera icon"
                  className="w-[90px] h-[90px] object-contain"
                />
              </div>

              <Button
                variant="ghost"
                size="icon"
                className="rounded-full w-10 h-10 p-2 flex items-center justify-center absolute right-0 top-1/2 -translate-y-1/2 z-10"
              >
                <ChevronRight className="w-6 h-6 text-[#3E485C]" />
              </Button>
            </div>

            {/* Help Content */}
            <div className="flex flex-col gap-2 items-center">
              <h3 className="text-[#525F7A] text-[22px] font-[510] leading-[1.193] tracking-[-0.029em] text-center w-[360px]">
                {features[0].title}
              </h3>
              <p className="text-[#525F7A] text-[14px] font-[400] leading-[1.193] tracking-[-0.03em] text-center w-[360px]">
                {features[0].description}
              </p>
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="absolute bottom-0 left-0 right-0 border-t border-[#E0E4EB] px-8 py-4 flex items-center justify-center gap-2">
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="ghost"
              className="text-[#525F7A] hover:text-[#0E1115] text-[16px] font-[590] leading-[1em] px-4 py-2 h-[40px] rounded-2xl"
            >
              Confidentiality
            </Button>
            <Button
              variant="ghost"
              className="text-[#525F7A] hover:text-[#0E1115] text-[16px] font-[590] leading-[1em] px-4 py-2 h-[40px] rounded-2xl"
            >
              Conditions
            </Button>
            <Button
              variant="ghost"
              className="text-[#525F7A] hover:text-[#0E1115] text-[16px] font-[590] leading-[1em] px-4 py-2 h-[40px] rounded-2xl"
            >
              Support
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
}
