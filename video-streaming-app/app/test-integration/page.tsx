"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useCreateMeeting, useMyMeetings, useHealthCheck } from "@/lib/hooks";
import { useAuth } from "@/lib/auth-context";

export default function TestIntegrationPage() {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [isRunning, setIsRunning] = useState(false);
  
  const { isAuthenticated, user } = useAuth();
  const createMeetingMutation = useCreateMeeting();
  const { data: meetings } = useMyMeetings();
  const { data: healthData, error: healthError } = useHealthCheck();

  const runTest = async (testName: string, testFn: () => Promise<boolean>) => {
    setIsRunning(true);
    try {
      const result = await testFn();
      setTestResults(prev => ({ ...prev, [testName]: result }));
      return result;
    } catch (error) {
      console.error(`Test ${testName} failed:`, error);
      setTestResults(prev => ({ ...prev, [testName]: false }));
      return false;
    } finally {
      setIsRunning(false);
    }
  };

  const testBackendConnection = async () => {
    return runTest("Backend Connection", async () => {
      return !healthError && !!healthData;
    });
  };

  const testAuthentication = async () => {
    return runTest("Authentication", async () => {
      return isAuthenticated && !!user;
    });
  };

  const testMeetingCreation = async () => {
    return runTest("Meeting Creation", async () => {
      const result = await createMeetingMutation.mutateAsync({
        title: "Test Meeting",
        description: "Integration test meeting",
        host_language: "en",
        is_public: true,
      });
      return !!result && !!result.id;
    });
  };

  const testWebSocketConnection = async () => {
    return runTest("WebSocket Connection", async () => {
      return new Promise((resolve) => {
        const ws = new WebSocket("ws://127.0.0.1:8001/ws/en/en");
        
        ws.onopen = () => {
          ws.close();
          resolve(true);
        };
        
        ws.onerror = () => {
          resolve(false);
        };
        
        // Timeout after 5 seconds
        setTimeout(() => {
          ws.close();
          resolve(false);
        }, 5000);
      });
    });
  };

  const runAllTests = async () => {
    await testBackendConnection();
    await testAuthentication();
    await testMeetingCreation();
    await testWebSocketConnection();
  };

  const getStatusBadge = (testName: string) => {
    if (!(testName in testResults)) {
      return <Badge variant="outline">Not Run</Badge>;
    }
    return testResults[testName] ? (
      <Badge className="bg-green-100 text-green-800">✓ Pass</Badge>
    ) : (
      <Badge className="bg-red-100 text-red-800">✗ Fail</Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Frontend-Backend Integration Test
          </h1>
          <p className="text-gray-600">
            Test the integration between the video streaming frontend and omnispeak backend
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Test Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Test Controls</CardTitle>
              <CardDescription>
                Run individual tests or all tests at once
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={runAllTests} 
                disabled={isRunning}
                className="w-full"
              >
                {isRunning ? "Running Tests..." : "Run All Tests"}
              </Button>
              
              <div className="grid gap-2">
                <Button 
                  variant="outline" 
                  onClick={testBackendConnection}
                  disabled={isRunning}
                  size="sm"
                >
                  Test Backend Connection
                </Button>
                <Button 
                  variant="outline" 
                  onClick={testAuthentication}
                  disabled={isRunning}
                  size="sm"
                >
                  Test Authentication
                </Button>
                <Button 
                  variant="outline" 
                  onClick={testMeetingCreation}
                  disabled={isRunning}
                  size="sm"
                >
                  Test Meeting Creation
                </Button>
                <Button 
                  variant="outline" 
                  onClick={testWebSocketConnection}
                  disabled={isRunning}
                  size="sm"
                >
                  Test WebSocket Connection
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                Status of integration tests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Backend Connection</span>
                  {getStatusBadge("Backend Connection")}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Authentication</span>
                  {getStatusBadge("Authentication")}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Meeting Creation</span>
                  {getStatusBadge("Meeting Creation")}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">WebSocket Connection</span>
                  {getStatusBadge("WebSocket Connection")}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
              <CardDescription>
                Current system information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Backend Health:</span>
                  <span className={healthError ? "text-red-600" : "text-green-600"}>
                    {healthError ? "Error" : "OK"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Authentication:</span>
                  <span className={isAuthenticated ? "text-green-600" : "text-red-600"}>
                    {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>User:</span>
                  <span>{user?.full_name || "None"}</span>
                </div>
                <div className="flex justify-between">
                  <span>My Meetings:</span>
                  <span>{meetings?.length || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API Endpoints */}
          <Card>
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
              <CardDescription>
                Backend endpoints being used
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm font-mono">
                <div>Health: GET /health</div>
                <div>Auth: POST /auth/login</div>
                <div>Meetings: POST /meetings/</div>
                <div>Languages: POST /meetings/:id/languages</div>
                <div>Chat: POST /chat</div>
                <div>WebSocket: ws://127.0.0.1:8001/ws/:src/:target</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
