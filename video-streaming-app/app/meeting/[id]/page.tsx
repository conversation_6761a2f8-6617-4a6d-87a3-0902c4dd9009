"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  MessageSquare,
  Users,
  PhoneOff,
  Send,
  Languages,
  Settings,
  X,
  Maximize2,
  Zap,
  Globe,
  Lock,
  LogOut,
  Volume2,
} from "lucide-react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import {
  useRoomMetadata,
  usePostRoomChat,
  useJoinRoom,
  useMeeting,
  useMeetingLanguages,
  useAddLanguageToMeeting,
  useUpdateMeeting,
  usePostChatMessage,
} from "@/lib/hooks";
import { SUPPORTED_LANGUAGES } from "@/lib/types";
import { AudioStreamingClient } from "@/lib/audio-streaming";
import { VideoStreamingClient } from "@/lib/video-streaming";
import { useAuth } from "@/lib/auth-context";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { LanguageManager } from "@/components/meeting/language-manager";
import { CloudflareStreamPlayer } from "@/components/meeting/cloudflare-stream-player";

function SpeakerMeetingPageContent() {
  const [isVideoOn, setIsVideoOn] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"chat" | "participants" | "languages">("chat");
  const [chatMessage, setChatMessage] = useState("");
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [participants, setParticipants] = useState<any[]>([]);
  const [sourceLanguage, setSourceLanguage] = useState("en-US");
  const [callDuration, setCallDuration] = useState(0);
  const [audioClient, setAudioClient] = useState<AudioStreamingClient | null>(
    null
  );
  const [showLanguageManager, setShowLanguageManager] = useState(false);
  const { logout, user } = useAuth();

  const searchParams = useSearchParams();
  const isGuest = searchParams.get("guest") === "true";
  const guestName = searchParams.get("name");
  const guestLanguage = searchParams.get("lang") || "en-US";

  const [audioStatus, setAudioStatus] = useState<any>({});
  const [videoClient, setVideoClient] = useState<VideoStreamingClient | null>(
    null
  );
  const [videoStatus, setVideoStatus] = useState<any>({});

  const videoRef = useRef<HTMLVideoElement>(null);
  const videoStreamRef = useRef<MediaStream | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);

  const router = useRouter();
  const params = useParams();
  const meetingId = params.id as string;

  // Use new backend API hooks
  const { data: meetingData, isLoading: meetingLoading } = useMeeting(
    parseInt(meetingId)
  );
  const { data: meetingLanguages } = useMeetingLanguages(parseInt(meetingId));
  const addLanguageMutation = useAddLanguageToMeeting();
  const updateMeetingMutation = useUpdateMeeting();
  const postChatMutation = usePostChatMessage();
  const joinRoomMutation = useJoinRoom();

  // Legacy support
  const { data: roomData } = useRoomMetadata(parseInt(meetingId));
  const postRoomChatMutation = usePostRoomChat();

  useEffect(() => {
    // Initialize audio client
    const audioClient = new AudioStreamingClient(
      setAudioStatus,
      (message) => {
        if (message.type === "chat") {
          setChatMessages((prev) => [...prev, message]);
        }
      },
      (error) => console.error("Audio error:", error)
    );
    setAudioClient(audioClient);

    // Initialize video client (only for speakers, not guests)
    if (!isGuest) {
      const videoClient = new VideoStreamingClient(
        setVideoStatus,
        (message) => {
          if (message.type === "video-frame") {
            // Handle incoming video frame for display
            console.log("Received video frame:", message.metadata);
          }
        },
        (error) => console.error("Video error:", error)
      );
      setVideoClient(videoClient);
    }

    // Start call duration timer
    const interval = setInterval(() => {
      setCallDuration((prev) => prev + 1);
    }, 1000);

    return () => {
      clearInterval(interval);
      audioClient.stopStreaming();
      if (videoClient) {
        videoClient.stopStreaming();
      }

      // Cleanup video streams
      if (videoStreamRef.current) {
        videoStreamRef.current.getTracks().forEach((track) => track.stop());
      }
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, [isGuest]);

  // Join room if guest
  useEffect(() => {
    if (isGuest && guestName) {
      const joinRoom = async () => {
        try {
          await joinRoomMutation.mutateAsync({
            room_id: parseInt(meetingId),
            viewer_id: `guest_${Date.now()}`,
            viewer_name: guestName,
          });

          // Start audio streaming for receiving translated audio (receive-only for guests)
          if (audioClient) {
            await audioClient.startStreaming(
              "auto", // Auto-detect source language
              guestLanguage as any,
              meetingId,
              true // receiveOnly = true for guests
            );
          }
        } catch (error) {
          console.error("Failed to join room:", error);
          alert("Failed to join meeting room");
          router.push("/");
        }
      };

      joinRoom();
    }
  }, [
    isGuest,
    guestName,
    meetingId,
    joinRoomMutation,
    audioClient,
    guestLanguage,
  ]);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${mins
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const toggleVideo = async () => {
    try {
      if (!isVideoOn) {
        // Request camera access and display feed
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: false,
        });

        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoStreamRef.current = stream;
        }

        // Start video streaming through WebSocket
        if (videoClient) {
          await videoClient.startStreaming(meetingId, "speaker");
        }
      } else {
        // Stop camera feed
        if (videoStreamRef.current) {
          videoStreamRef.current.getTracks().forEach((track) => track.stop());
          videoStreamRef.current = null;
        }

        if (videoRef.current) {
          videoRef.current.srcObject = null;
        }

        // Stop video streaming
        if (videoClient) {
          videoClient.stopStreaming();
        }
      }
      setIsVideoOn(!isVideoOn);
    } catch (error) {
      console.error("Error accessing camera:", error);
      alert("Could not access camera. Please check permissions.");
    }
  };

  const toggleMic = async () => {
    try {
      if (!isMicOn) {
        // Start audio streaming
        if (audioClient) {
          await audioClient.startStreaming(
            sourceLanguage as any,
            "en-US",
            meetingId
          );
        }
      } else {
        // Stop audio streaming
        if (audioClient) {
          audioClient.stopStreaming();
        }
      }
      setIsMicOn(!isMicOn);
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access microphone. Please check permissions.");
    }
  };

  const endCall = () => {
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (audioClient) {
      audioClient.stopStreaming();
    }
    router.push("/");
  };

  const sendMessage = async () => {
    if (chatMessage.trim()) {
      try {
        // Use the new chat API endpoint
        await postChatMutation.mutateAsync({
          text: chatMessage,
          room_id: meetingId,
        });
        setChatMessage("");
      } catch (error) {
        console.error("Failed to send message:", error);
        // Fallback to legacy room chat API
        try {
          await postRoomChatMutation.mutateAsync({
            room_id: parseInt(meetingId),
            text: chatMessage,
            viewer_id: isGuest ? `guest_${Date.now()}` : "speaker",
            viewer_name: isGuest ? guestName || "Guest" : "Speaker",
          });
          setChatMessage("");
        } catch (fallbackError) {
          console.error("Failed to send message with fallback:", fallbackError);
        }
      }
    }
  };

  // Render guest view if user is a guest
  if (isGuest) {
    return (
      <div className="h-screen bg-[#F3F4F7] flex flex-col">
        {/* Header for Guests */}
        <header className="flex items-center justify-between px-4 py-4 h-[88px]">
          <div className="flex items-center gap-4">
            {/* Logo Group */}
            <div className="flex items-center gap-[19.2px]">
              {/* Main Logo */}
              <div className="w-16 h-16 bg-[#18191B] rounded-[12.8px] flex items-center justify-center">
                <svg
                  width="64"
                  height="64"
                  viewBox="0 0 64 64"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect width="64" height="64" rx="12.8" fill="#18191B" />
                  <path
                    d="M39.1503 24.4211C40.9371 22.9747 43.8116 16.3123 45.1828 13.2597C46.5539 10.2071 49.4952 7.88945 55.5327 9.90578C50.1084 28.8044 58.0614 33.1455 49.3779 42.6848C44.0002 48.5926 33.0103 46.595 23.8745 44.5773C23.5695 45.5374 23.3008 46.6065 23.0782 47.7989C20.6811 60.639 8.18839 49.1212 9.19777 44.1459C9.80259 41.1648 16.1811 42.8782 23.8745 44.5773C26.854 35.1977 33.3005 36.2178 34.1162 34.3579C21.5909 39.3639 28.897 22.5951 11.5776 21.0112C15.0166 10.1763 26.1885 7.03555 32.7634 10.3375C39.3383 13.6395 36.0696 26.915 39.1503 24.4211Z"
                    fill="white"
                    stroke="white"
                    strokeWidth="3.2"
                  />
                  <circle cx="50.8389" cy="12.7308" r="2.92219" fill="black" />
                </svg>
              </div>

              {/* Logos Live Text */}
              <div className="text-[#0E1115] text-[32px] font-[590] leading-[1.193] tracking-[-0.03em]">
                Logos Live
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Guest Name */}
            <div className="flex items-center gap-2 bg-[#E0E4EB] rounded-full px-3 py-1">
              <Avatar className="w-6 h-6">
                <AvatarFallback className="text-xs bg-[#2060DF] text-white">
                  {guestName?.charAt(0) || "G"}
                </AvatarFallback>
              </Avatar>
              <span className="text-[#0E1115] text-sm font-[590]">
                {guestName}
              </span>
            </div>

            {/* Language Selection */}
            <Select value={guestLanguage} onValueChange={() => {}}>
              <SelectTrigger className="w-32 bg-[#E0E4EB] border-0 rounded-full h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SUPPORTED_LANGUAGES.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Leave Button */}
            <Button
              onClick={endCall}
              className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white rounded-full px-4 py-2 h-10 font-[590]"
            >
              <PhoneOff className="w-4 h-4 mr-2" />
              Leave
            </Button>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Video Area */}
          <div className="flex-1 relative bg-[#18191B] rounded-2xl mx-4 mb-4">
            {/* Find the stream URL for the guest's selected language */}
            {(() => {
              const selectedLanguageStream = meetingLanguages?.find(
                (lang) => lang.language_code === guestLanguage
              );

              if (selectedLanguageStream?.stream_url) {
                return (
                  <CloudflareStreamPlayer
                    streamUrl={selectedLanguageStream.stream_url}
                    videoUid={selectedLanguageStream.cloudflare_video_uid}
                    className="w-full h-full"
                    autoplay={true}
                    muted={false}
                  />
                );
              }

              // Fallback to placeholder if no stream available
              return (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <Avatar className="w-32 h-32 mx-auto mb-4">
                      <AvatarImage src="/placeholder-user.jpg" />
                      <AvatarFallback className="text-4xl bg-[#525F7A] text-white">
                        {user?.full_name?.charAt(0) || "SP"}
                      </AvatarFallback>
                    </Avatar>
                    <h2 className="text-white text-xl font-[590] mb-2">
                      {user?.full_name || "Speaker"}
                    </h2>
                    <p className="text-[#94A3B8]">
                      Listening to translated audio in {guestLanguage}...
                    </p>
                  </div>
                </div>
              );
            })()}

                {/* Audio Status */}
                <div className="mt-4 text-sm text-[#94A3B8]">
                  <div className="flex items-center justify-center gap-2">
                    <Volume2 className="w-4 h-4" />
                    <span>
                      Audio: {audioStatus.audioStatus || "Connecting..."}
                    </span>
                  </div>
                </div>

                {/* Meeting Info */}
                <div className="mt-6 text-xs text-[#94A3B8]">
                  <div className="flex items-center justify-center gap-2">
                    <Lock className="w-3 h-3" />
                    <span>Meeting ID: {meetingId}</span>
                    <span>•</span>
                    <span>Duration: {formatDuration(callDuration)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Sidebar */}
          <div className="w-80 bg-white border-l border-[#E0E4EB] flex flex-col mr-4 mb-4 rounded-2xl">
            <div className="p-4 border-b border-[#E0E4EB]">
              <h3 className="font-[590] text-[#0E1115] flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                Chat with Speaker
              </h3>
            </div>

            <div className="flex-1 p-4 overflow-y-auto space-y-4">
              {chatMessages.length === 0 ? (
                <div className="text-center text-[#94A3B8] py-8">
                  <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Send a message to start the conversation</p>
                </div>
              ) : (
                chatMessages.map((msg, index) => (
                  <div key={msg.id || index} className="flex gap-3">
                    <Avatar className="w-8 h-8 flex-shrink-0">
                      <AvatarFallback className="text-xs bg-[#E0E4EB] text-[#0E1115]">
                        {msg.user
                          ?.split(" ")
                          .map((n: string) => n[0])
                          .join("") || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-[590] text-[#0E1115] truncate">
                          {msg.user || "User"}
                        </span>
                        <span className="text-xs text-[#525F7A]">
                          {msg.time ||
                            new Date().toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                        </span>
                      </div>
                      <p className="text-sm text-[#525F7A] break-words">
                        {msg.message || msg.text}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>

            <div className="p-4 border-t border-[#E0E4EB]">
              <div className="flex gap-2">
                <Input
                  placeholder="Send a message to the speaker..."
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && sendMessage()}
                  className="flex-1 bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-4 py-2 text-[16px] font-[400] h-[40px]"
                />
                <Button
                  onClick={sendMessage}
                  disabled={postChatMutation.isPending}
                  className="bg-[#2060DF] hover:bg-[#2060DF]/90 text-white rounded-full px-3 h-[40px]"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Speaker view
  return (
    <div className="bg-[#F3F4F7] w-full h-screen mx-auto relative flex flex-col">
      {/* Header */}
      {false ? (
        <header className="flex items-center justify-between px-4 py-4 h-[88px]">
          <div className="flex items-center gap-4">
            {/* Logo Group */}
            <div className="flex items-center gap-[19.2px]">
              {/* Main Logo */}
              <div className="w-16 h-16 bg-[#18191B] rounded-[12.8px] flex items-center justify-center">
                <svg
                  width="64"
                  height="64"
                  viewBox="0 0 64 64"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect width="64" height="64" rx="12.8" fill="#18191B" />
                  <path
                    d="M39.1503 24.4211C40.9371 22.9747 43.8116 16.3123 45.1828 13.2597C46.5539 10.2071 49.4952 7.88945 55.5327 9.90578C50.1084 28.8044 58.0614 33.1455 49.3779 42.6848C44.0002 48.5926 33.0103 46.595 23.8745 44.5773C23.5695 45.5374 23.3008 46.6065 23.0782 47.7989C20.6811 60.639 8.18839 49.1212 9.19777 44.1459C9.80259 41.1648 16.1811 42.8782 23.8745 44.5773C26.854 35.1977 33.3005 36.2178 34.1162 34.3579C21.5909 39.3639 28.897 22.5951 11.5776 21.0112C15.0166 10.1763 26.1885 7.03555 32.7634 10.3375C39.3383 13.6395 36.0696 26.915 39.1503 24.4211Z"
                    fill="white"
                    stroke="white"
                    strokeWidth="3.2"
                  />
                  <circle cx="50.8389" cy="12.7308" r="2.92219" fill="black" />
                </svg>
              </div>
              {/* Logo Text */}
              <span className="text-[#0E1115] text-[24px] whitespace-nowrap font-[590] leading-[1.193] tracking-[-0.02em]">
                Logos Live
              </span>
            </div>
          </div>

          {/* Meeting Code Display */}
          <div className="flex-1 flex justify-center">
            {meetingData?.meeting_code && (
              <div className="bg-[#E0E4EB] rounded-lg px-4 py-2">
                <div className="text-center">
                  <p className="text-xs text-[#525F7A] font-medium">Meeting Code</p>
                  <p className="text-lg font-[590] text-[#0E1115] tracking-wider">
                    {meetingData.meeting_code}
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <div className="relative flex items-center gap-2">
              {/* <Avatar className="w-12 h-12">
                    <AvatarImage src="/placeholder-user.jpg" />
                    <AvatarFallback className="bg-[#18191B] text-[#F3F4F7] text-[16px] font-[590] leading-[1em]">
                      {user?.full_name
                        ?.split(" ")
                        .map((n) => n[0])
                        .join("") || "U"}
                    </AvatarFallback>
                  </Avatar> */}
              {/* Badge */}
              {/* <div className="absolute top-[33.61px] left-[34px] w-[10px] h-[10px] bg-[#2060DF] rounded-full"></div> */}

              {/* Logout Button */}
              <Button
                onClick={() => {
                  endCall();
                  logout();
                }}
                variant="ghost"
                size="sm"
                className="rounded-full w-12 h-12 p-2 flex items-center justify-center hover:bg-red-100"
                title="Logout"
              >
                <LogOut className="w-5 h-5 text-[#DC2626]" />
              </Button>
            </div>
          </div>
        </header>
      ) : (
        <div className="py-2"></div>
      )}

      {/* Main Video Area */}
      <div className="flex-1 px-4 pb-0">
        <div className="w-full h-full bg-black rounded-[24px] relative overflow-hidden">
          {/* Background Video/Image */}
          <img
            src="/meeting-background.jpg"
            alt="Video feed background"
            className="absolute inset-0 w-full h-full object-cover"
          />
          {/* Video Element for actual camera feed */}
          <video
            ref={videoRef}
            className="absolute inset-0 w-full h-full object-cover scale-x-[-1]"
            autoPlay
            muted
            playsInline
            style={{ display: isVideoOn ? "block" : "none" }}
          />

          {/* Top Info Bar */}
          <div className="absolute top-0 left-0 right-0 flex justify-between items-center px-4 py-4 opacity-70">
            <div className="flex justify-between items-end flex-1">
              <span className="text-[#E4E5E7] text-[14px] font-[400] leading-[1em] drop-shadow-[0_2px_4px_rgba(0,0,0,0.5)]">
                {user?.full_name}
              </span>
            </div>
            <div className="flex justify-end items-center gap-2 w-[283px]">
              <span className="text-[#E4E5E7] text-[12px] font-[400] leading-[1em] drop-shadow-[0_2px_4px_rgba(0,0,0,0.5)]">
                Call: {formatDuration(callDuration)}
              </span>
            </div>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-0 left-0 right-0 flex mx-auto justify-between items-center px-4 py-2">
            {/* Left Controls */}
            <div
              className="flex items-center gap-2 invisible pointer-events-none"
              aria-hidden
            >
              <Button
                variant="ghost"
                size="sm"
                className="rounded-full w-12 h-12 p-2 bg-[rgba(120,125,135,0.1)] flex items-center justify-center"
              >
                <Maximize2 className="w-6 h-6 text-[#303236]" />
              </Button>
              <Button
                onClick={toggleVideo}
                className="rounded-full w-12 h-12 p-2 bg-[#18191B] flex items-center justify-center"
              >
                <Globe className="w-6 h-6 text-[#F3F4F7]" />
              </Button>
            </div>

            {/* Center Controls */}
            <div className="flex items-center gap-2 bg-[rgba(120,125,135,0.02)] backdrop-blur-[20px] rounded-full px-2 py-2 justify-center">
              <Button
                onClick={toggleVideo}
                className={`rounded-full w-16 h-12 p-2 flex items-center justify-center ${
                  isVideoOn ? "bg-[#33B253]" : "bg-[#33B253]"
                }`}
              >
                {isVideoOn ? (
                  <Video className="w-6 h-6 text-[#F3F4F7]" />
                ) : (
                  <VideoOff className="w-[21.75px] h-[16.5px] text-[#F3F4F7]" />
                )}
              </Button>

              <Button
                onClick={toggleMic}
                className={`rounded-full w-16 h-12 p-2 flex items-center justify-center ${
                  isMicOn ? "bg-[#A3ADC2]" : "bg-[#A3ADC2]"
                }`}
              >
                {isMicOn ? (
                  <Mic className="w-6 h-6 text-[#0F1116]" />
                ) : (
                  <MicOff className="w-6 h-6 text-[#E4E5E7]" />
                )}
              </Button>

              <Button
                onClick={endCall}
                className="rounded-full w-16 h-12 p-2 bg-[#F93939] flex items-center justify-center"
              >
                <PhoneOff className="w-[22.4px] h-[8.7px] text-[#F3F4F7]" />
              </Button>
            </div>

            <div
              className="flex items-center gap-2 invisible pointer-events-none"
              aria-hidden
            >
              <Button
                variant="ghost"
                size="sm"
                className="rounded-full w-12 h-12 p-2 bg-[rgba(120,125,135,0.1)] flex items-center justify-center"
              >
                <Maximize2 className="w-6 h-6 text-[#303236]" />
              </Button>
              <Button
                onClick={toggleVideo}
                className="rounded-full w-12  h-12 p-2 bg-[#18191B] flex items-center justify-center"
              >
                <Globe className="w-6 h-6 text-[#F3F4F7]" />
              </Button>
            </div>
          </div>

          {/* Chat Button */}
          <div className="absolute bottom-4 right-4">
            <Button
              onClick={() => setIsChatOpen(!isChatOpen)}
              className="rounded-full h-16 w-16 bg-[#2060DF] flex items-center justify-center gap-2"
            >
              <MessageSquare className="w-6 h-6 text-[#F3F4F7]" />
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom Status Bar */}
      <div className="flex items-center gap-4 px-4 py-3 h-auto">
        <div className="flex items-center gap-2 w-[283px]">
          <Lock className="w-4 h-4 text-[#525F7A]" />
          <span className="text-[#525F7A] text-[12px] font-[400] leading-[1em]">
            {formatDuration(callDuration).slice(0, 5)}
          </span>
          <span className="text-[#525F7A] text-[12px] font-[400] leading-[1em]">
            {meetingId}
          </span>
        </div>
      </div>

      {/* Chat/Participants Modal */}
      {isChatOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="w-96 h-[600px] bg-white rounded-xl shadow-2xl flex flex-col">
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex gap-1">
                <Button
                  variant={activeTab === "chat" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("chat")}
                  className="rounded-lg"
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Chat
                </Button>
                <Button
                  variant={activeTab === "participants" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("participants")}
                  className="rounded-lg"
                >
                  <Users className="w-4 h-4 mr-2" />
                  People ({participants.length})
                </Button>
                {!isGuest && (
                  <Button
                    variant={activeTab === "languages" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveTab("languages")}
                    className="rounded-lg"
                  >
                    <Languages className="w-4 h-4 mr-2" />
                    Languages
                  </Button>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsChatOpen(false)}
                className="rounded-full w-8 h-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex-1 overflow-hidden">
              {activeTab === "chat" ? (
                <div className="flex flex-col h-full">
                  <div className="flex-1 p-4 overflow-y-auto space-y-4">
                    {chatMessages.map((msg) => (
                      <div key={msg.id} className="flex gap-3">
                        <Avatar className="w-8 h-8 flex-shrink-0">
                          <AvatarFallback className="text-xs bg-zinc-200">
                            {msg.user
                              ?.split(" ")
                              .map((n: string) => n[0])
                              .join("") || "G"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-zinc-900 truncate">
                              {msg.user || "Guest"}
                            </span>
                            <span className="text-xs text-zinc-500">
                              {msg.time}
                            </span>
                          </div>
                          <p className="text-sm text-zinc-700 break-words">
                            {msg.message}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="p-4 border-t">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Type a message..."
                        value={chatMessage}
                        onChange={(e) => setChatMessage(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && sendMessage()}
                        className="flex-1 rounded-lg"
                      />
                      <Button onClick={sendMessage} className="rounded-lg px-3">
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ) : activeTab === "participants" ? (
                <div className="p-4 space-y-3">
                  {participants.map((participant) => (
                    <div
                      key={participant.id}
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-zinc-50"
                    >
                      <Avatar className="w-10 h-10">
                        <AvatarFallback className="bg-zinc-200">
                          {participant.name
                            ?.split(" ")
                            .map((n: string) => n[0])
                            .join("") || "G"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-zinc-900 truncate">
                          {participant.name}
                        </p>
                        <p className="text-xs text-zinc-500">
                          {participant.role || "Guest"}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4">
                  <LanguageManager
                    meetingId={parseInt(meetingId)}
                    currentLanguages={meetingLanguages || []}
                    maxLanguages={5}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function MeetingPageWrapper() {
  const searchParams = useSearchParams();
  const isGuest = searchParams.get("guest") === "true";

  // Guests don't need authentication, speakers do
  if (isGuest) {
    return <SpeakerMeetingPageContent />;
  }

  return (
    <ProtectedRoute requireAuth={true}>
      <SpeakerMeetingPageContent />
    </ProtectedRoute>
  );
}

export default function SpeakerMeetingPage() {
  return <MeetingPageWrapper />;
}
