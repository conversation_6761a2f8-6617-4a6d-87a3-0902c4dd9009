"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter, useParams } from "next/navigation";
import { useGuestJoin } from "@/lib/hooks";
import { SUPPORTED_LANGUAGES } from "@/lib/types";
import { Modal } from "@/components/ui/modal";

export default function JoinModal() {
  const [guestName, setGuestName] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("en-US");

  const router = useRouter();
  const params = useParams();
  const meetingCode = params.code as string;

  const guestJoinMutation = useGuestJoin();

  const handleJoinMeeting = async () => {
    if (!guestName.trim()) {
      alert("Please enter your name");
      return;
    }

    try {
      await guestJoinMutation.mutateAsync({
        guest_name: guestN<PERSON>,
      });

      // Navigate to the meeting room as guest
      router.push(
        `/meeting/${meetingCode}?guest=true&name=${encodeURIComponent(
          guestName
        )}&lang=${selectedLanguage}`
      );
    } catch (error) {
      console.error("Failed to join meeting:", error);
      alert("Failed to join meeting. Please check the meeting code.");
      router.push("/");
    }
  };

  return (
    <Modal>
      <div className="bg-[#F3F4F7] rounded-2xl w-full max-w-md relative overflow-hidden">
        {/* Header */}
        <div className="bg-[#18191B] px-8 h-32 pb-4 flex items-end relative rounded-t-2xl">
          {/* Header */}
          <div className="text-left">
            <h1 className="text-[#F3F4F7] text-[28px] font-[590] leading-[1.193] tracking-[-0.03em]">
              Join Meeting
            </h1>
          </div>
        </div>

        {/* Form Content */}
        <div className="px-8 py-8">
          {/* Meeting Code Display */}
          <div className="mb-6 text-left">
            <p className="text-[#525F7A] text-[16px] font-[400] leading-[1.193] tracking-[-0.03em]">
              Meeting Code:{" "}
              <span className="font-[590] text-[#0E1115]">{meetingCode}</span>
            </p>
          </div>

          <div className="space-y-6">
            {/* Name Input */}
            <div>
              <Input
                placeholder="Enter your name ..."
                value={guestName}
                onChange={(e) => setGuestName(e.target.value)}
                className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 text-[20px] font-[400] leading-[1.193] tracking-[-0.03em] focus:ring-2 focus:ring-[#2060DF]"
              />
            </div>

            {/* Language Selection */}
            <div>
              <Select
                value={selectedLanguage}
                onValueChange={setSelectedLanguage}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language..." />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Join Button */}
            <Button
              onClick={handleJoinMeeting}
              disabled={guestJoinMutation.isPending}
              className="w-full bg-[#2060DF] hover:bg-[#2060DF]/90 text-[#F3F4F7] rounded-full py-3 text-[20px] font-[590] leading-[1em] shadow-lg"
            >
              {guestJoinMutation.isPending ? "Joining..." : "Join Meeting"}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
