"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { useRegister } from "@/lib/hooks";
import { SUPPORTED_LANGUAGES } from "@/lib/types";
import { Modal } from "@/components/ui/modal";

export default function RegisterModal() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [preferredLanguage, setPreferredLanguage] = useState("en-US");
  const [originalLanguage, setOriginalLanguage] = useState("en-US");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const router = useRouter();
  const registerMutation = useRegister();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!agreedToTerms) {
      alert("Please agree to the Terms of Service and Privacy Policy");
      return;
    }

    if (password !== confirmPassword) {
      alert("Passwords do not match");
      return;
    }

    if (password.length < 6) {
      alert("Password must be at least 6 characters long");
      return;
    }

    try {
      const result = await registerMutation.mutateAsync({
        email,
        password,
        full_name: fullName,
        preferred_language: preferredLanguage,
        original_language: originalLanguage,
      });

      if (result.success) {
        alert("Registration successful! Please log in.");
        router.push("/auth/login");
      } else {
        alert(result.message || "Registration failed");
      }
    } catch (error) {
      console.error("Registration failed:", error);
      alert("Registration failed. Please try again.");
    }
  };

  const handleLogin = () => {
    router.push("/auth/login");
  };

  return (
    <Modal className="max-w-lg">
      <div className="bg-[#F3F4F7] rounded-2xl w-full relative overflow-hidden shadow-lg">
        {/* Header */}
        <div className="bg-[#18191B] px-8 h-32 pb-4 flex items-end relative rounded-t-2xl">
          {/* Header */}
          <div className="text-left">
            <h1 className="text-[#F3F4F7] text-[32px] font-[590] leading-[1.193] tracking-[-0.03em]">
              Create Account
            </h1>
          </div>
        </div>

        {/* Form Content */}
        <div className="px-8 py-8 max-h-[60vh] overflow-y-auto">
          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Full Name */}
            <div>
              <label className="block text-[14px] font-[590] text-[#0E1115] mb-2 leading-[1.193] tracking-[-0.03em]">
                Full Name
              </label>
              <Input
                type="text"
                placeholder="Enter your full name"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                required
                className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 text-[16px] font-[400] leading-[1.193] tracking-[-0.03em] h-[44px] focus:ring-2 focus:ring-[#2060DF]"
              />
            </div>

            {/* Email */}
            <div>
              <label className="block text-[14px] font-[590] text-[#0E1115] mb-2 leading-[1.193] tracking-[-0.03em]">
                Email Address
              </label>
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 text-[16px] font-[400] leading-[1.193] tracking-[-0.03em] h-[44px] focus:ring-2 focus:ring-[#2060DF]"
              />
            </div>

            {/* Password */}
            <div>
              <label className="block text-[14px] font-[590] text-[#0E1115] mb-2 leading-[1.193] tracking-[-0.03em]">
                Password
              </label>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  placeholder="Create a password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 pr-12 text-[16px] font-[400] leading-[1.193] tracking-[-0.03em] h-[44px] focus:ring-2 focus:ring-[#2060DF]"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#525F7A] hover:text-[#0E1115] w-8 h-8 p-0"
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Confirm Password */}
            <div>
              <label className="block text-[14px] font-[590] text-[#0E1115] mb-2 leading-[1.193] tracking-[-0.03em]">
                Confirm Password
              </label>
              <div className="relative">
                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 pr-12 text-[16px] font-[400] leading-[1.193] tracking-[-0.03em] h-[44px] focus:ring-2 focus:ring-[#2060DF]"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#525F7A] hover:text-[#0E1115] w-8 h-8 p-0"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Language Preferences */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-[14px] font-[590] text-[#0E1115] mb-2 leading-[1.193] tracking-[-0.03em]">
                  Original Language
                </label>
                <Select
                  value={originalLanguage}
                  onValueChange={setOriginalLanguage}
                >
                  <SelectTrigger className="bg-[#E0E4EB] border-0 text-[#0E1115] rounded-[20px] px-5 py-3 text-[14px] font-[400] leading-[1.193] tracking-[-0.03em] h-[44px] focus:ring-2 focus:ring-[#2060DF]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.filter(
                      (lang) => lang.code !== "auto"
                    ).map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-[14px] font-[590] text-[#0E1115] mb-2 leading-[1.193] tracking-[-0.03em]">
                  Preferred Language
                </label>
                <Select
                  value={preferredLanguage}
                  onValueChange={setPreferredLanguage}
                >
                  <SelectTrigger className="bg-[#E0E4EB] border-0 text-[#0E1115] rounded-[20px] px-5 py-3 text-[14px] font-[400] leading-[1.193] tracking-[-0.03em] h-[44px] focus:ring-2 focus:ring-[#2060DF]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.filter(
                      (lang) => lang.code !== "auto"
                    ).map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start gap-3">
              <Checkbox
                id="terms"
                checked={agreedToTerms}
                onCheckedChange={(checked) =>
                  setAgreedToTerms(checked as boolean)
                }
                className="mt-1 border-[#525F7A]"
              />
              <label
                htmlFor="terms"
                className="text-[#525F7A] text-[14px] font-[400] leading-[1.193] tracking-[-0.03em]"
              >
                I agree to the{" "}
                <a href="#" className="text-[#2060DF] hover:underline">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#" className="text-[#2060DF] hover:underline">
                  Privacy Policy
                </a>
              </label>
            </div>

            {/* Register Button */}
            <Button
              type="submit"
              disabled={registerMutation.isPending}
              className="w-full bg-[#2060DF] hover:bg-[#2060DF]/90 text-[#F3F4F7] rounded-full py-3 text-[18px] font-[590] leading-[1em] h-[48px] shadow-lg"
            >
              {registerMutation.isPending
                ? "Creating Account..."
                : "Create Account"}
            </Button>

            {/* Login Link */}
            <div className="text-center">
              <span className="text-[#525F7A] text-[14px] font-[400] leading-[1.193] tracking-[-0.03em]">
                Already have an account?{" "}
              </span>
              <button
                type="button"
                onClick={handleLogin}
                className="text-[#2060DF] hover:underline text-[14px] font-[590] leading-[1.193] tracking-[-0.03em]"
              >
                Sign In
              </button>
            </div>
          </form>
        </div>
      </div>
    </Modal>
  );
}
