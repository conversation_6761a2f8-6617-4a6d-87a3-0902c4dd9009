"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSpeakerLogin } from "@/lib/hooks";
import { useAuth } from "@/lib/auth-context";
import { Modal } from "@/components/ui/modal";

export default function LoginModal() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [agreedToPrivacy, setAgreedToPrivacy] = useState(false);
  const router = useRouter();
  const { login } = useAuth();

  const speakerLoginMutation = useSpeakerLogin();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!agreedToPrivacy) {
      alert("Please agree to the Privacy Policy");
      return;
    }

    try {
      const result = await speakerLoginMutation.mutateAsync({
        email,
        password,
      });

      if (result.access_token) {
        // Create mock user data since the API doesn't return user info
        const mockUser = {
          id: 1,
          email: email,
          full_name: "Speaker User",
          preferred_language: "en-US",
          original_language: "en-US",
          created_at: new Date().toISOString(),
        };

        // Use auth context to login
        login(result.access_token, mockUser);
        router.back(); // Close the modal
      } else if (result.error) {
        alert(result.error);
      }
    } catch (error) {
      console.error("Login failed:", error);
      alert("Login failed. Please check your credentials.");
    }
  };

  const handleSignUp = () => {
    router.push("/auth/register");
  };

  return (
    <Modal>
      <div className="bg-[#F3F4F7] rounded-2xl w-full max-w-md relative overflow-hidden shadow-lg">
        {/* Header */}
        <div className="bg-[#18191B] px-8 h-32 pb-4 flex items-end relative rounded-t-2xl">
          {/* Header */}
          <div className="text-left">
            <h1 className="text-[#F3F4F7] text-[28px] font-[590] leading-[1.193] tracking-[-0.03em]">
              Hello!
            </h1>
          </div>
        </div>

        {/* Form Content */}
        <div className="px-8 py-8">
          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Privacy Policy Checkbox */}
            <div className="flex items-start gap-3">
              <Checkbox
                id="privacy"
                checked={agreedToPrivacy}
                onCheckedChange={(checked) =>
                  setAgreedToPrivacy(checked as boolean)
                }
                className="mt-1 border-[#525F7A]"
              />
              <label
                htmlFor="privacy"
                className="text-[#525F7A] text-[14px] font-[400] leading-[1.193] tracking-[-0.03em]"
              >
                You agree to our{" "}
                <a href="#" className="text-[#2060DF] hover:underline">
                  Privacy Policy
                </a>
              </label>
            </div>

            {/* Email Input */}
            <div>
              <Input
                type="email"
                placeholder="Enter E-mail ..."
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 text-[20px] font-[400] leading-[1.193] tracking-[-0.03em] h-[50px] focus:ring-2 focus:ring-[#2060DF]"
              />
            </div>

            {/* Password Input */}
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="bg-[#E0E4EB] border-0 text-[#0E1115] placeholder-[#525F7A] rounded-[20px] px-5 py-3 pr-12 text-[20px] font-[400] leading-[1.193] tracking-[-0.03em] h-[50px] focus:ring-2 focus:ring-[#2060DF]"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#525F7A] hover:text-[#0E1115] w-8 h-8 p-0"
              >
                {showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </Button>
            </div>

            {/* Sign Up Button */}
            <Button
              type="submit"
              disabled={speakerLoginMutation.isPending}
              className="w-full bg-[#2060DF] hover:bg-[#2060DF]/90 text-[#F3F4F7] rounded-full py-3 text-[20px] font-[590] leading-[1em] h-[50px] shadow-lg"
            >
              {speakerLoginMutation.isPending ? "Logging In..." : "Login"}
            </Button>

            {/* Login Link */}
            <div className="text-left">
              <span className="text-[#525F7A] text-[14px] font-[400] leading-[1.193] tracking-[-0.03em]">
                Don't have an account?{" "}
              </span>
              <button
                type="button"
                onClick={handleSignUp}
                className="text-[#2060DF] hover:underline text-[14px] font-[590] leading-[1.193] tracking-[-0.03em]"
              >
                Sign Up
              </button>
            </div>
          </form>
        </div>
      </div>
    </Modal>
  );
}
