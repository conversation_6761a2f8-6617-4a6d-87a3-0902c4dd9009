# VideoMeet - New Generation Meetings

A modern video streaming application with real-time translation capabilities, built with Next.js, TanStack Query, and WebRTC.

## Features

### 🎥 Video Streaming

- One-way video streaming from speaker to guests
- HD video quality with WebRTC
- Mock video streaming implementation for development

### 🎤 Audio Translation

- Real-time audio streaming with translation
- Support for multiple languages (English, Ukrainian, Russian, Polish, German, French, Spanish, Chinese)
- WebSocket-based audio transmission
- Mock audio streaming based on debug HTML client

### 👥 User Roles

- **Speakers**: Can create meetings, stream video/audio, manage participants
- **Guests**: Can join meetings, receive translated audio, send messages

### 💬 Real-time Chat

- Live messaging between speakers and guests
- Message history and timestamps
- Clean, modern chat interface

### 🌐 Multi-language Support

- Auto-detect source language
- Real-time translation to preferred language
- Language selection for guests

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **UI Components**: Radix UI, Tailwind CSS
- **Data Fetching**: TanStack Query (React Query)
- **Audio/Video**: WebRTC, WebSocket
- **Styling**: Tailwind CSS with custom design system

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Backend API server running on `http://localhost:8000`
- WebSocket server running on `ws://127.0.0.1:8001`

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd video-streaming-app
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Edit `.env.local`:

```
NEXT_PUBLIC_API_URL=http://localhost:8000
```

4. Start the development server:

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Application Structure

### Pages

- `/` - Home page with meeting creation and join options (shows different UI for authenticated/non-authenticated users)
- `/auth/login` - Speaker login page (also available as modal)
- `/auth/register` - Speaker registration page (also available as modal)
- `/meeting/[id]` - Speaker meeting interface
- `/join/[code]` - Guest join interface (also available as modal)

### Key Components

- `lib/api.ts` - API client with TanStack Query integration
- `lib/audio-streaming.ts` - Audio streaming client based on debug HTML
- `lib/hooks.ts` - React Query hooks for API operations
- `lib/types.ts` - TypeScript type definitions

## API Integration

The application integrates with the backend API documented in `app/backend-docs.json`:

### Authentication Endpoints

- `POST /auth/register` - Speaker registration
- `POST /auth/login` - User login
- `POST /auth/guest` - Guest join
- `POST /meetings/auth/speaker_login` - Speaker authentication

### Meeting Endpoints

- `POST /meetings/rooms/create` - Create meeting room
- `POST /meetings/rooms/join_room` - Join meeting room
- `POST /meetings/rooms/leave_room` - Leave meeting room
- `GET /meetings/rooms/metadata/{room_id}` - Get room metadata
- `GET /meetings/rooms/list_rooms` - List available rooms

### Chat Endpoints

- `GET /meetings/rooms/chat` - Get chat messages
- `POST /meetings/rooms/chat` - Send chat message

## Audio Streaming

The audio streaming implementation is based on the debug HTML client and includes:

- Microphone access with echo cancellation and noise suppression
- Real-time audio processing and resampling to 16kHz
- WebSocket connection for audio transmission
- Volume monitoring and packet statistics
- Support for multiple source and target languages

## Design System

The application follows a clean, modern design language inspired by Google Meet:

- **Colors**: Blue primary (#3B82F6), Gray neutrals
- **Typography**: Work Sans for headings, Open Sans for body text
- **Components**: Rounded corners, subtle shadows, clean spacing
- **Layout**: Responsive grid system, mobile-first approach

## Development

### Running Tests

```bash
npm test
```

### Building for Production

```bash
npm run build
npm start
```

### Code Quality

```bash
npm run lint
npm run type-check
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
