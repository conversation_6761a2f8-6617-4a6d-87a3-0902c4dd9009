Frontend-Backend Communication Flow Documentation

## Overview

This document outlines the complete communication flow between the video-streaming-app frontend and the omnispeak-backend for all user roles and scenarios.

## Base Configuration

- **Backend API Base URL**: `http://localhost:8001`
- **WebSocket URL**: `ws://127.0.0.1:8001/ws/{src_lang}/{target_lang}`
- **Authentication**: Cookie-based sessions + optional token storage

---

## 1. Authentication Flows

### 1.1 Speaker Registration

```http
POST /auth/register
Content-Type: application/json

Request Body:
{
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "<PERSON>",
  "preferred_language": "en",
  "original_language": "en"
}

Response:
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "<PERSON>",
    "preferred_language": "en",
    "original_language": "en",
    "voice_sample_url": null,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "session_token": "abc123..."
}
```

### 1.2 Speaker Login

```http
POST /auth/login
Content-Type: application/json

Request Body:
{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "John Speaker",
    "preferred_language": "en",
    "original_language": "en",
    "voice_sample_url": null,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "session_token": "abc123..."
}
```

### 1.3 Guest Authentication

```http
POST /auth/guest
Content-Type: application/json

Request Body:
{
  "guest_name": "Guest User"
}

Response:
{
  "success": true,
  "message": "Guest session created",
  "session_token": "guest_abc123..."
}
```

### 1.4 Authentication Status Check

```http
GET /auth/status
Credentials: include (cookies)

Response:
{
  "authenticated": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "John Speaker",
    "preferred_language": "en",
    "original_language": "en",
    "voice_sample_url": null,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.5 Logout

```http
POST /auth/logout
Credentials: include (cookies)

Response:
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## 2. Meeting Management Flows

### 2.1 Speaker Creates Meeting

```http
POST /meetings/
Content-Type: application/json
Credentials: include (cookies)

Request Body:
{
  "title": "Weekly Team Meeting",
  "description": "Our weekly sync meeting",
  "host_language": "en",
  "is_public": true
}

Response:
{
  "id": 123,
  "host_id": 1,
  "title": "Weekly Team Meeting",
  "description": "Our weekly sync meeting",
  "host_language": "en",
  "meeting_code": "ABC123",
  "status": "created",
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z",
  "started_at": null,
  "ended_at": null,
  "max_languages": 5,
  "lang_limit": 5
}
```

### 2.2 Get Speaker's Meetings

```http
GET /meetings/my-meetings
Credentials: include (cookies)

Response:
[
  {
    "id": 123,
    "host_id": 1,
    "title": "Weekly Team Meeting",
    "description": "Our weekly sync meeting",
    "host_language": "en",
    "meeting_code": "ABC123",
    "status": "created",
    "is_public": true,
    "created_at": "2024-01-01T00:00:00Z",
    "started_at": null,
    "ended_at": null,
    "max_languages": 5,
    "lang_limit": 5
  }
]
```

### 2.3 Get Meeting by ID

```http
GET /meetings/123
Credentials: include (cookies)

Response:
{
  "id": 123,
  "host_id": 1,
  "title": "Weekly Team Meeting",
  "description": "Our weekly sync meeting",
  "host_language": "en",
  "meeting_code": "ABC123",
  "status": "created",
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z",
  "started_at": null,
  "ended_at": null,
  "max_languages": 5,
  "lang_limit": 5
}
```

### 2.4 Get Meeting by Code (Guest Flow)

```http
GET /meetings/code/ABC123

Response:
{
  "id": 123,
  "host_id": 1,
  "title": "Weekly Team Meeting",
  "description": "Our weekly sync meeting",
  "host_language": "en",
  "meeting_code": "ABC123",
  "status": "active",
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z",
  "started_at": "2024-01-01T01:00:00Z",
  "ended_at": null,
  "max_languages": 5,
  "lang_limit": 5
}
```

### 2.5 Update Meeting

```http
PUT /meetings/123
Content-Type: application/json
Credentials: include (cookies)

Request Body:
{
  "title": "Updated Meeting Title",
  "description": "Updated description",
  "status": "active"
}

Response:
{
  "id": 123,
  "host_id": 1,
  "title": "Updated Meeting Title",
  "description": "Updated description",
  "host_language": "en",
  "meeting_code": "ABC123",
  "status": "active",
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z",
  "started_at": "2024-01-01T01:00:00Z",
  "ended_at": null,
  "max_languages": 5,
  "lang_limit": 5
}
```

---

## 3. Language Management Flows

### 3.1 Add Language to Meeting

```http
POST /meetings/123/languages
Content-Type: application/json
Credentials: include (cookies)

Request Body:
{
  "language_code": "es",
  "language_name": "Spanish"
}

Response:
{
  "id": 456,
  "meeting_id": 123,
  "language_code": "es",
  "language_name": "Spanish",
  "stream_url": null,
  "cloudflare_video_uid": null,
  "status": "pending",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 3.2 Get Meeting Languages

```http
GET /meetings/123/languages

Response:
[
  {
    "id": 456,
    "meeting_id": 123,
    "language_code": "es",
    "language_name": "Spanish",
    "stream_url": "https://customer-xxx.cloudflarestream.com/abc123/manifest/video.m3u8",
    "cloudflare_video_uid": "abc123",
    "status": "ready",
    "created_at": "2024-01-01T00:00:00Z"
  },
  {
    "id": 457,
    "meeting_id": 123,
    "language_code": "fr",
    "language_name": "French",
    "stream_url": "https://customer-xxx.cloudflarestream.com/def456/manifest/video.m3u8",
    "cloudflare_video_uid": "def456",
    "status": "streaming",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

---

## 4. Guest Join Flow

### 4.1 Guest Joins Meeting

```http
POST /meetings/join
Content-Type: application/json

Request Body:
{
  "meeting_code": "ABC123",
  "display_name": "Guest User",
  "selected_language": "es"
}

Response:
{
  "success": true,
  "message": "Joined meeting successfully",
  "meeting": {
    "id": 123,
    "title": "Weekly Team Meeting",
    "host_language": "en",
    "status": "active"
  },
  "selected_language": {
    "language_code": "es",
    "language_name": "Spanish",
    "stream_url": "https://customer-xxx.cloudflarestream.com/abc123/manifest/video.m3u8"
  }
}
```

---

## 5. Chat System Flows

### 5.1 Send Chat Message

```http
POST /chat
Content-Type: application/json

Request Body:
{
  "text": "Hello from guest!",
  "room_id": "123"
}

Response:
{
  "success": true,
  "message": "Message sent successfully"
}
```

---

## 6. WebSocket Communication Flows

### 6.1 WebSocket Connection

```
WebSocket URL: ws://127.0.0.1:8001/ws/{src_lang}/{target_lang}

Examples:
- Speaker (English): ws://127.0.0.1:8001/ws/en/en
- Guest (Spanish): ws://127.0.0.1:8001/ws/en/es
- Guest (French): ws://127.0.0.1:8001/ws/en/fr
```

### 6.2 Speaker WebSocket Flow

#### 6.2.1 Connection and Setup

```javascript
// 1. Connect to WebSocket
const ws = new WebSocket("ws://127.0.0.1:8001/ws/en/en");

// 2. Send start recording message (JSON)
ws.send(
  JSON.stringify({
    type: "start_recording",
    width: 1280,
    height: 720,
  })
);

// 3. Send audio data (binary, < 10KB chunks)
ws.send(audioBuffer); // ArrayBuffer with PCM audio data

// 4. Send video data (binary, >= 10KB chunks)
ws.send(videoBuffer); // ArrayBuffer with JPEG frame data

// 5. Send stop recording message (JSON)
ws.send(
  JSON.stringify({
    type: "stop_recording",
  })
);
```

#### 6.2.2 Audio Data Format

- **Format**: PCM 16-bit mono
- **Sample Rate**: 44100 Hz
- **Chunk Size**: < 10,000 bytes
- **Frequency**: Continuous stream

#### 6.2.3 Video Data Format

- **Format**: JPEG frames
- **Size**: >= 10,000 bytes per frame
- **Frame Rate**: 30 FPS
- **Resolution**: 1280x720 (configurable)

### 6.3 Guest WebSocket Flow

#### 6.3.1 Connection for Audio Reception

```javascript
// 1. Connect to WebSocket for translated audio
const ws = new WebSocket("ws://127.0.0.1:8001/ws/en/es"); // English to Spanish

// 2. Receive translated audio data (binary)
ws.onmessage = (event) => {
  if (event.data instanceof ArrayBuffer) {
    // This is translated audio data
    playAudioBuffer(event.data);
  }
};
```

---

## 7. Complete User Journey Flows

### 7.1 Speaker Complete Flow

```
1. AUTHENTICATION
   POST /auth/login → Get session + user data

2. CREATE MEETING
   POST /meetings/ → Get meeting with code (e.g., "ABC123")

3. ADD LANGUAGES (Optional, up to 5)
   POST /meetings/123/languages (Spanish)
   POST /meetings/123/languages (French)
   POST /meetings/123/languages (German)

4. START MEETING
   PUT /meetings/123 (status: "active")

5. START STREAMING
   WebSocket: ws://127.0.0.1:8001/ws/en/en
   - Send start_recording JSON message
   - Stream audio data (binary, < 10KB)
   - Stream video data (binary, >= 10KB)

6. RECEIVE CHAT MESSAGES
   - Backend pushes chat messages via WebSocket or polling

7. END MEETING
   - Send stop_recording JSON message
   - Close WebSocket
   PUT /meetings/123 (status: "ended")

8. LOGOUT
   POST /auth/logout
```

### 7.2 Guest Complete Flow

```
1. GUEST AUTHENTICATION
   POST /auth/guest → Get guest session

2. FIND MEETING BY CODE
   GET /meetings/code/ABC123 → Get meeting details

3. GET AVAILABLE LANGUAGES
   GET /meetings/123/languages → List of available languages

4. JOIN MEETING
   POST /meetings/join → Join with selected language

5. RECEIVE TRANSLATED AUDIO
   WebSocket: ws://127.0.0.1:8001/ws/en/es (English to Spanish)
   - Receive binary audio data
   - Play translated audio

6. VIEW TRANSLATED VIDEO
   - Use Cloudflare Stream URL from language data
   - Display video player with stream

7. SEND CHAT MESSAGES
   POST /chat → Send messages to speaker

8. LEAVE MEETING
   - Close WebSocket connection
   - Navigate away or close browser
```

---

## 8. Data Flow Summary

### 8.1 Speaker Data Flow

```
Speaker Browser → Frontend → Backend API → Database
                ↓
Speaker Browser → WebSocket → Backend → Video/Audio Processing
                                    ↓
                              Cloudflare Stream (per language)
                                    ↓
                              Guest Browsers (via stream URLs)
```

### 8.2 Guest Data Flow

```
Guest Browser → Frontend → Backend API (meeting lookup)
             ↓
Guest Browser → WebSocket → Backend → Translated Audio
             ↓
Guest Browser → Cloudflare Stream → Translated Video
             ↓
Guest Browser → Backend API → Chat Messages
```

---
